{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/lib/validation.ts"], "sourcesContent": ["import { z } from 'zod'\n\n// Common validation schemas\nexport const emailSchema = z\n  .string()\n  .email('Please enter a valid email address')\n  .min(1, 'Email is required')\n\nexport const passwordSchema = z\n  .string()\n  .min(8, 'Password must be at least 8 characters')\n  .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')\n  .regex(/[a-z]/, 'Password must contain at least one lowercase letter')\n  .regex(/[0-9]/, 'Password must contain at least one number')\n  .regex(/[^A-Za-z0-9]/, 'Password must contain at least one special character')\n\nexport const phoneSchema = z\n  .string()\n  .regex(/^\\+?[1-9]\\d{1,14}$/, 'Please enter a valid phone number')\n  .min(1, 'Phone number is required')\n\nexport const priceSchema = z\n  .number()\n  .positive('Price must be positive')\n  .max(1000000, 'Price cannot exceed $1,000,000')\n\nexport const locationSchema = z.object({\n  latitude: z.number().min(-90).max(90),\n  longitude: z.number().min(-180).max(180),\n  address: z.string().min(1, 'Address is required')\n})\n\n// User validation schemas\nexport const signUpSchema = z.object({\n  email: emailSchema,\n  password: passwordSchema,\n  confirmPassword: z.string(),\n  fullName: z\n    .string()\n    .min(2, 'Full name must be at least 2 characters')\n    .max(50, 'Full name cannot exceed 50 characters')\n    .regex(/^[a-zA-Z\\s\\u0600-\\u06FF\\u0750-\\u077F]+$/, 'Full name can only contain letters and spaces'),\n  phone: phoneSchema,\n  acceptTerms: z.boolean().refine(val => val === true, 'You must accept the terms and conditions')\n}).refine(data => data.password === data.confirmPassword, {\n  message: 'Passwords do not match',\n  path: ['confirmPassword']\n})\n\nexport const signInSchema = z.object({\n  email: emailSchema,\n  password: z.string().min(1, 'Password is required')\n})\n\nexport const resetPasswordSchema = z.object({\n  email: emailSchema\n})\n\nexport const changePasswordSchema = z.object({\n  currentPassword: z.string().min(1, 'Current password is required'),\n  newPassword: passwordSchema,\n  confirmPassword: z.string()\n}).refine(data => data.newPassword === data.confirmPassword, {\n  message: 'Passwords do not match',\n  path: ['confirmPassword']\n})\n\n// Product validation schemas\nexport const productSchema = z.object({\n  title: z\n    .string()\n    .min(3, 'Title must be at least 3 characters')\n    .max(100, 'Title cannot exceed 100 characters')\n    .regex(/^[a-zA-Z0-9\\s\\u0600-\\u06FF\\u0750-\\u077F\\-_.,!?()]+$/, 'Title contains invalid characters'),\n  description: z\n    .string()\n    .min(10, 'Description must be at least 10 characters')\n    .max(2000, 'Description cannot exceed 2000 characters'),\n  price: priceSchema,\n  currency: z.enum(['USD', 'SYP', 'EUR', 'TRY']),\n  condition: z.enum(['new', 'like_new', 'good', 'fair', 'poor']),\n  categoryId: z.string().uuid('Invalid category'),\n  location: z.string().min(1, 'Location is required'),\n  images: z\n    .array(z.string().url('Invalid image URL'))\n    .min(1, 'At least one image is required')\n    .max(10, 'Maximum 10 images allowed'),\n  tags: z\n    .array(z.string().min(1).max(20))\n    .max(10, 'Maximum 10 tags allowed')\n    .optional(),\n  isNegotiable: z.boolean().optional(),\n  deliveryOptions: z\n    .array(z.enum(['pickup', 'delivery', 'shipping']))\n    .min(1, 'At least one delivery option is required')\n})\n\nexport const productUpdateSchema = productSchema.partial()\n\n// Message validation schemas\nexport const messageSchema = z.object({\n  content: z\n    .string()\n    .min(1, 'Message cannot be empty')\n    .max(1000, 'Message cannot exceed 1000 characters'),\n  recipientId: z.string().uuid('Invalid recipient'),\n  productId: z.string().uuid('Invalid product').optional(),\n  attachments: z\n    .array(z.string().url('Invalid attachment URL'))\n    .max(5, 'Maximum 5 attachments allowed')\n    .optional()\n})\n\n// Review validation schemas\nexport const reviewSchema = z.object({\n  rating: z.number().min(1, 'Rating must be at least 1').max(5, 'Rating cannot exceed 5'),\n  comment: z\n    .string()\n    .min(10, 'Comment must be at least 10 characters')\n    .max(500, 'Comment cannot exceed 500 characters')\n    .optional(),\n  userId: z.string().uuid('Invalid user'),\n  productId: z.string().uuid('Invalid product').optional()\n})\n\n// Search validation schemas\nexport const searchSchema = z.object({\n  query: z.string().max(100, 'Search query too long').optional(),\n  category: z.string().uuid('Invalid category').optional(),\n  location: z.string().max(100, 'Location too long').optional(),\n  priceMin: z.number().min(0).optional(),\n  priceMax: z.number().min(0).optional(),\n  condition: z.enum(['new', 'like_new', 'good', 'fair', 'poor']).optional(),\n  sortBy: z.enum(['relevance', 'price_low', 'price_high', 'newest', 'oldest', 'distance']).optional(),\n  page: z.number().min(1).optional(),\n  limit: z.number().min(1).max(100).optional()\n})\n\n// File upload validation\nexport const fileUploadSchema = z.object({\n  file: z.instanceof(File),\n  maxSize: z.number().default(5 * 1024 * 1024), // 5MB\n  allowedTypes: z.array(z.string()).default(['image/jpeg', 'image/png', 'image/webp'])\n})\n\n// Utility functions for validation\nexport function validateFile(file: File, maxSize = 5 * 1024 * 1024, allowedTypes = ['image/jpeg', 'image/png', 'image/webp']) {\n  const errors: string[] = []\n\n  if (file.size > maxSize) {\n    errors.push(`File size must be less than ${Math.round(maxSize / 1024 / 1024)}MB`)\n  }\n\n  if (!allowedTypes.includes(file.type)) {\n    errors.push(`File type must be one of: ${allowedTypes.join(', ')}`)\n  }\n\n  return {\n    isValid: errors.length === 0,\n    errors\n  }\n}\n\nexport function sanitizeInput(input: string): string {\n  return input\n    .trim()\n    .replace(/[<>]/g, '') // Remove potential HTML tags\n    .replace(/javascript:/gi, '') // Remove javascript: protocol\n    .replace(/on\\w+=/gi, '') // Remove event handlers\n}\n\nexport function validateImageUrl(url: string): boolean {\n  try {\n    const urlObj = new URL(url)\n    return ['http:', 'https:'].includes(urlObj.protocol) &&\n           /\\.(jpg|jpeg|png|gif|webp)$/i.test(urlObj.pathname)\n  } catch {\n    return false\n  }\n}\n\nexport function validatePhoneNumber(phone: string): boolean {\n  // Remove all non-digit characters except +\n  const cleaned = phone.replace(/[^\\d+]/g, '')\n  \n  // Check if it's a valid international format\n  const internationalRegex = /^\\+[1-9]\\d{1,14}$/\n  \n  // Check if it's a valid local format (assuming Syrian numbers)\n  const syrianRegex = /^(09|08)\\d{8}$/\n  \n  return internationalRegex.test(cleaned) || syrianRegex.test(cleaned)\n}\n\nexport function validatePassword(password: string): { isValid: boolean; errors: string[] } {\n  const errors: string[] = []\n\n  if (password.length < 8) {\n    errors.push('Password must be at least 8 characters long')\n  }\n\n  if (!/[A-Z]/.test(password)) {\n    errors.push('Password must contain at least one uppercase letter')\n  }\n\n  if (!/[a-z]/.test(password)) {\n    errors.push('Password must contain at least one lowercase letter')\n  }\n\n  if (!/[0-9]/.test(password)) {\n    errors.push('Password must contain at least one number')\n  }\n\n  if (!/[^A-Za-z0-9]/.test(password)) {\n    errors.push('Password must contain at least one special character')\n  }\n\n  // Check for common weak passwords\n  const commonPasswords = ['password', '123456', 'qwerty', 'abc123', 'password123']\n  if (commonPasswords.includes(password.toLowerCase())) {\n    errors.push('Password is too common')\n  }\n\n  return {\n    isValid: errors.length === 0,\n    errors\n  }\n}\n\n// Rate limiting validation\nexport function createRateLimiter(maxRequests: number, windowMs: number) {\n  const requests = new Map<string, number[]>()\n\n  return function isRateLimited(identifier: string): boolean {\n    const now = Date.now()\n    const windowStart = now - windowMs\n\n    if (!requests.has(identifier)) {\n      requests.set(identifier, [])\n    }\n\n    const userRequests = requests.get(identifier)!\n    \n    // Remove old requests outside the window\n    const validRequests = userRequests.filter(time => time > windowStart)\n    \n    if (validRequests.length >= maxRequests) {\n      return true // Rate limited\n    }\n\n    // Add current request\n    validRequests.push(now)\n    requests.set(identifier, validRequests)\n\n    return false // Not rate limited\n  }\n}\n\n// XSS protection\nexport function escapeHtml(unsafe: string): string {\n  return unsafe\n    .replace(/&/g, '&amp;')\n    .replace(/</g, '&lt;')\n    .replace(/>/g, '&gt;')\n    .replace(/\"/g, '&quot;')\n    .replace(/'/g, '&#039;')\n}\n\n// SQL injection protection (for raw queries)\nexport function escapeSql(input: string): string {\n  return input.replace(/'/g, \"''\")\n}\n\n// CSRF token validation\nexport function generateCSRFToken(): string {\n  return Array.from(crypto.getRandomValues(new Uint8Array(32)))\n    .map(b => b.toString(16).padStart(2, '0'))\n    .join('')\n}\n\nexport function validateCSRFToken(token: string, sessionToken: string): boolean {\n  return token === sessionToken && token.length === 64\n}\n\n// Content Security Policy helpers\nexport const CSP_DIRECTIVES = {\n  'default-src': [\"'self'\"],\n  'script-src': [\"'self'\", \"'unsafe-inline'\", 'https://www.google-analytics.com'],\n  'style-src': [\"'self'\", \"'unsafe-inline'\", 'https://fonts.googleapis.com'],\n  'img-src': [\"'self'\", 'data:', 'https:', 'blob:'],\n  'font-src': [\"'self'\", 'https://fonts.gstatic.com'],\n  'connect-src': [\"'self'\", 'https://api.supabase.co'],\n  'frame-ancestors': [\"'none'\"],\n  'base-uri': [\"'self'\"],\n  'form-action': [\"'self'\"]\n}\n\nexport function generateCSPHeader(): string {\n  return Object.entries(CSP_DIRECTIVES)\n    .map(([directive, sources]) => `${directive} ${sources.join(' ')}`)\n    .join('; ')\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;;AAGO,MAAM,cAAc,yLAAA,CAAA,IAAC,CACzB,MAAM,GACN,KAAK,CAAC,sCACN,GAAG,CAAC,GAAG;AAEH,MAAM,iBAAiB,yLAAA,CAAA,IAAC,CAC5B,MAAM,GACN,GAAG,CAAC,GAAG,0CACP,KAAK,CAAC,SAAS,uDACf,KAAK,CAAC,SAAS,uDACf,KAAK,CAAC,SAAS,6CACf,KAAK,CAAC,gBAAgB;AAElB,MAAM,cAAc,yLAAA,CAAA,IAAC,CACzB,MAAM,GACN,KAAK,CAAC,sBAAsB,qCAC5B,GAAG,CAAC,GAAG;AAEH,MAAM,cAAc,yLAAA,CAAA,IAAC,CACzB,MAAM,GACN,QAAQ,CAAC,0BACT,GAAG,CAAC,SAAS;AAET,MAAM,iBAAiB,yLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACrC,UAAU,yLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC;IAClC,WAAW,yLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC;IACpC,SAAS,yLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC7B;AAGO,MAAM,eAAe,yLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACnC,OAAO;IACP,UAAU;IACV,iBAAiB,yLAAA,CAAA,IAAC,CAAC,MAAM;IACzB,UAAU,yLAAA,CAAA,IAAC,CACR,MAAM,GACN,GAAG,CAAC,GAAG,2CACP,GAAG,CAAC,IAAI,yCACR,KAAK,CAAC,2CAA2C;IACpD,OAAO;IACP,aAAa,yLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,MAAM,CAAC,CAAA,MAAO,QAAQ,MAAM;AACvD,GAAG,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,KAAK,eAAe,EAAE;IACxD,SAAS;IACT,MAAM;QAAC;KAAkB;AAC3B;AAEO,MAAM,eAAe,yLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACnC,OAAO;IACP,UAAU,yLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC9B;AAEO,MAAM,sBAAsB,yLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1C,OAAO;AACT;AAEO,MAAM,uBAAuB,yLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3C,iBAAiB,yLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACnC,aAAa;IACb,iBAAiB,yLAAA,CAAA,IAAC,CAAC,MAAM;AAC3B,GAAG,MAAM,CAAC,CAAA,OAAQ,KAAK,WAAW,KAAK,KAAK,eAAe,EAAE;IAC3D,SAAS;IACT,MAAM;QAAC;KAAkB;AAC3B;AAGO,MAAM,gBAAgB,yLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACpC,OAAO,yLAAA,CAAA,IAAC,CACL,MAAM,GACN,GAAG,CAAC,GAAG,uCACP,GAAG,CAAC,KAAK,sCACT,KAAK,CAAC,uDAAuD;IAChE,aAAa,yLAAA,CAAA,IAAC,CACX,MAAM,GACN,GAAG,CAAC,IAAI,8CACR,GAAG,CAAC,MAAM;IACb,OAAO;IACP,UAAU,yLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAO;QAAO;QAAO;KAAM;IAC7C,WAAW,yLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAO;QAAY;QAAQ;QAAQ;KAAO;IAC7D,YAAY,yLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,CAAC;IAC5B,UAAU,yLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,QAAQ,yLAAA,CAAA,IAAC,CACN,KAAK,CAAC,yLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,sBACrB,GAAG,CAAC,GAAG,kCACP,GAAG,CAAC,IAAI;IACX,MAAM,yLAAA,CAAA,IAAC,CACJ,KAAK,CAAC,yLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,KAC5B,GAAG,CAAC,IAAI,2BACR,QAAQ;IACX,cAAc,yLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;IAClC,iBAAiB,yLAAA,CAAA,IAAC,CACf,KAAK,CAAC,yLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAU;QAAY;KAAW,GAC/C,GAAG,CAAC,GAAG;AACZ;AAEO,MAAM,sBAAsB,cAAc,OAAO;AAGjD,MAAM,gBAAgB,yLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACpC,SAAS,yLAAA,CAAA,IAAC,CACP,MAAM,GACN,GAAG,CAAC,GAAG,2BACP,GAAG,CAAC,MAAM;IACb,aAAa,yLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,CAAC;IAC7B,WAAW,yLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,CAAC,mBAAmB,QAAQ;IACtD,aAAa,yLAAA,CAAA,IAAC,CACX,KAAK,CAAC,yLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,2BACrB,GAAG,CAAC,GAAG,iCACP,QAAQ;AACb;AAGO,MAAM,eAAe,yLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACnC,QAAQ,yLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,6BAA6B,GAAG,CAAC,GAAG;IAC9D,SAAS,yLAAA,CAAA,IAAC,CACP,MAAM,GACN,GAAG,CAAC,IAAI,0CACR,GAAG,CAAC,KAAK,wCACT,QAAQ;IACX,QAAQ,yLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,CAAC;IACxB,WAAW,yLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,CAAC,mBAAmB,QAAQ;AACxD;AAGO,MAAM,eAAe,yLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACnC,OAAO,yLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,yBAAyB,QAAQ;IAC5D,UAAU,yLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,CAAC,oBAAoB,QAAQ;IACtD,UAAU,yLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,qBAAqB,QAAQ;IAC3D,UAAU,yLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,QAAQ;IACpC,UAAU,yLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,QAAQ;IACpC,WAAW,yLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAO;QAAY;QAAQ;QAAQ;KAAO,EAAE,QAAQ;IACvE,QAAQ,yLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAa;QAAa;QAAc;QAAU;QAAU;KAAW,EAAE,QAAQ;IACjG,MAAM,yLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,QAAQ;IAChC,OAAO,yLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,KAAK,QAAQ;AAC5C;AAGO,MAAM,mBAAmB,yLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACvC,MAAM,yLAAA,CAAA,IAAC,CAAC,UAAU,CAAC;IACnB,SAAS,yLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,OAAO,CAAC,IAAI,OAAO;IACvC,cAAc,yLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,yLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,OAAO,CAAC;QAAC;QAAc;QAAa;KAAa;AACrF;AAGO,SAAS,aAAa,IAAU,EAAE,UAAU,IAAI,OAAO,IAAI,EAAE,eAAe;IAAC;IAAc;IAAa;CAAa;IAC1H,MAAM,SAAmB,EAAE;IAE3B,IAAI,KAAK,IAAI,GAAG,SAAS;QACvB,OAAO,IAAI,CAAC,CAAC,4BAA4B,EAAE,KAAK,KAAK,CAAC,UAAU,OAAO,MAAM,EAAE,CAAC;IAClF;IAEA,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;QACrC,OAAO,IAAI,CAAC,CAAC,0BAA0B,EAAE,aAAa,IAAI,CAAC,OAAO;IACpE;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAEO,SAAS,cAAc,KAAa;IACzC,OAAO,MACJ,IAAI,GACJ,OAAO,CAAC,SAAS,IAAI,6BAA6B;KAClD,OAAO,CAAC,iBAAiB,IAAI,8BAA8B;KAC3D,OAAO,CAAC,YAAY,IAAI,wBAAwB;;AACrD;AAEO,SAAS,iBAAiB,GAAW;IAC1C,IAAI;QACF,MAAM,SAAS,IAAI,IAAI;QACvB,OAAO;YAAC;YAAS;SAAS,CAAC,QAAQ,CAAC,OAAO,QAAQ,KAC5C,8BAA8B,IAAI,CAAC,OAAO,QAAQ;IAC3D,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEO,SAAS,oBAAoB,KAAa;IAC/C,2CAA2C;IAC3C,MAAM,UAAU,MAAM,OAAO,CAAC,WAAW;IAEzC,6CAA6C;IAC7C,MAAM,qBAAqB;IAE3B,+DAA+D;IAC/D,MAAM,cAAc;IAEpB,OAAO,mBAAmB,IAAI,CAAC,YAAY,YAAY,IAAI,CAAC;AAC9D;AAEO,SAAS,iBAAiB,QAAgB;IAC/C,MAAM,SAAmB,EAAE;IAE3B,IAAI,SAAS,MAAM,GAAG,GAAG;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,eAAe,IAAI,CAAC,WAAW;QAClC,OAAO,IAAI,CAAC;IACd;IAEA,kCAAkC;IAClC,MAAM,kBAAkB;QAAC;QAAY;QAAU;QAAU;QAAU;KAAc;IACjF,IAAI,gBAAgB,QAAQ,CAAC,SAAS,WAAW,KAAK;QACpD,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAGO,SAAS,kBAAkB,WAAmB,EAAE,QAAgB;IACrE,MAAM,WAAW,IAAI;IAErB,OAAO,SAAS,cAAc,UAAkB;QAC9C,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,cAAc,MAAM;QAE1B,IAAI,CAAC,SAAS,GAAG,CAAC,aAAa;YAC7B,SAAS,GAAG,CAAC,YAAY,EAAE;QAC7B;QAEA,MAAM,eAAe,SAAS,GAAG,CAAC;QAElC,yCAAyC;QACzC,MAAM,gBAAgB,aAAa,MAAM,CAAC,CAAA,OAAQ,OAAO;QAEzD,IAAI,cAAc,MAAM,IAAI,aAAa;YACvC,OAAO,KAAK,eAAe;;QAC7B;QAEA,sBAAsB;QACtB,cAAc,IAAI,CAAC;QACnB,SAAS,GAAG,CAAC,YAAY;QAEzB,OAAO,MAAM,mBAAmB;;IAClC;AACF;AAGO,SAAS,WAAW,MAAc;IACvC,OAAO,OACJ,OAAO,CAAC,MAAM,SACd,OAAO,CAAC,MAAM,QACd,OAAO,CAAC,MAAM,QACd,OAAO,CAAC,MAAM,UACd,OAAO,CAAC,MAAM;AACnB;AAGO,SAAS,UAAU,KAAa;IACrC,OAAO,MAAM,OAAO,CAAC,MAAM;AAC7B;AAGO,SAAS;IACd,OAAO,MAAM,IAAI,CAAC,OAAO,eAAe,CAAC,IAAI,WAAW,MACrD,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG,MACpC,IAAI,CAAC;AACV;AAEO,SAAS,kBAAkB,KAAa,EAAE,YAAoB;IACnE,OAAO,UAAU,gBAAgB,MAAM,MAAM,KAAK;AACpD;AAGO,MAAM,iBAAiB;IAC5B,eAAe;QAAC;KAAS;IACzB,cAAc;QAAC;QAAU;QAAmB;KAAmC;IAC/E,aAAa;QAAC;QAAU;QAAmB;KAA+B;IAC1E,WAAW;QAAC;QAAU;QAAS;QAAU;KAAQ;IACjD,YAAY;QAAC;QAAU;KAA4B;IACnD,eAAe;QAAC;QAAU;KAA0B;IACpD,mBAAmB;QAAC;KAAS;IAC7B,YAAY;QAAC;KAAS;IACtB,eAAe;QAAC;KAAS;AAC3B;AAEO,SAAS;IACd,OAAO,OAAO,OAAO,CAAC,gBACnB,GAAG,CAAC,CAAC,CAAC,WAAW,QAAQ,GAAK,GAAG,UAAU,CAAC,EAAE,QAAQ,IAAI,CAAC,MAAM,EACjE,IAAI,CAAC;AACV"}}, {"offset": {"line": 325, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\nimport type { NextRequest } from 'next/server'\nimport { generateCSPHeader, createRateLimiter } from '@/lib/validation'\n\n// Rate limiters for different endpoints\nconst generalRateLimit = createRateLimiter(100, 60 * 1000) // 100 requests per minute\nconst authRateLimit = createRateLimiter(5, 60 * 1000) // 5 auth attempts per minute\nconst apiRateLimit = createRateLimiter(50, 60 * 1000) // 50 API calls per minute\nconst uploadRateLimit = createRateLimiter(10, 60 * 1000) // 10 uploads per minute\n\n// Security headers\nconst securityHeaders = {\n  // Content Security Policy\n  'Content-Security-Policy': generateCSPHeader(),\n  \n  // Prevent clickjacking\n  'X-Frame-Options': 'DENY',\n  \n  // Prevent MIME type sniffing\n  'X-Content-Type-Options': 'nosniff',\n  \n  // Enable XSS protection\n  'X-XSS-Protection': '1; mode=block',\n  \n  // Referrer policy\n  'Referrer-Policy': 'strict-origin-when-cross-origin',\n  \n  // Permissions policy\n  'Permissions-Policy': 'camera=(), microphone=(), geolocation=(self), payment=()',\n  \n  // HSTS (HTTP Strict Transport Security)\n  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',\n  \n  // Prevent caching of sensitive pages\n  'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',\n  'Pragma': 'no-cache',\n  'Expires': '0',\n  \n  // Cross-Origin policies\n  'Cross-Origin-Embedder-Policy': 'require-corp',\n  'Cross-Origin-Opener-Policy': 'same-origin',\n  'Cross-Origin-Resource-Policy': 'same-origin'\n}\n\n// Paths that require authentication\nconst protectedPaths = [\n  '/dashboard',\n  '/profile',\n  '/messages',\n  '/favorites',\n  '/sell',\n  '/settings'\n]\n\n// Paths that require admin access\nconst adminPaths = [\n  '/admin'\n]\n\n// API paths that need rate limiting\nconst rateLimitedPaths = {\n  auth: ['/api/auth/signin', '/api/auth/signup', '/api/auth/reset-password'],\n  api: ['/api/products', '/api/users', '/api/messages'],\n  upload: ['/api/upload']\n}\n\nexport function middleware(request: NextRequest) {\n  const response = NextResponse.next()\n  const { pathname } = request.nextUrl\n  const userAgent = request.headers.get('user-agent') || ''\n  const ip = getClientIP(request)\n\n  // Add security headers to all responses\n  Object.entries(securityHeaders).forEach(([key, value]) => {\n    response.headers.set(key, value)\n  })\n\n  // Bot detection and blocking\n  if (isBot(userAgent) && !isAllowedBot(userAgent)) {\n    console.log(`Blocked bot: ${userAgent} from ${ip}`)\n    return new NextResponse('Access Denied', { status: 403 })\n  }\n\n  // Rate limiting\n  if (isRateLimited(pathname, ip)) {\n    console.log(`Rate limited: ${ip} on ${pathname}`)\n    return new NextResponse('Too Many Requests', { \n      status: 429,\n      headers: {\n        'Retry-After': '60'\n      }\n    })\n  }\n\n  // CSRF protection for state-changing requests\n  if (['POST', 'PUT', 'DELETE', 'PATCH'].includes(request.method)) {\n    const csrfToken = request.headers.get('x-csrf-token')\n    const sessionToken = request.cookies.get('csrf-token')?.value\n\n    if (!csrfToken || !sessionToken || csrfToken !== sessionToken) {\n      console.log(`CSRF token mismatch: ${ip} on ${pathname}`)\n      return new NextResponse('CSRF Token Mismatch', { status: 403 })\n    }\n  }\n\n  // Authentication check for protected routes\n  if (protectedPaths.some(path => pathname.startsWith(path))) {\n    const token = request.cookies.get('auth-token')?.value\n    \n    if (!token || !isValidToken(token)) {\n      const loginUrl = new URL('/auth/signin', request.url)\n      loginUrl.searchParams.set('redirect', pathname)\n      return NextResponse.redirect(loginUrl)\n    }\n  }\n\n  // Admin access check\n  if (adminPaths.some(path => pathname.startsWith(path))) {\n    const token = request.cookies.get('auth-token')?.value\n    \n    if (!token || !isValidToken(token) || !isAdminToken(token)) {\n      return new NextResponse('Access Denied', { status: 403 })\n    }\n  }\n\n  // Geo-blocking (if needed)\n  if (isBlockedCountry(request)) {\n    return new NextResponse('Service not available in your region', { status: 451 })\n  }\n\n  // Add request ID for tracking\n  const requestId = generateRequestId()\n  response.headers.set('X-Request-ID', requestId)\n\n  // Log security events\n  logSecurityEvent(request, ip, userAgent)\n\n  return response\n}\n\nfunction getClientIP(request: NextRequest): string {\n  const forwarded = request.headers.get('x-forwarded-for')\n  const realIP = request.headers.get('x-real-ip')\n  \n  if (forwarded) {\n    return forwarded.split(',')[0].trim()\n  }\n  \n  if (realIP) {\n    return realIP\n  }\n  \n  return request.ip || 'unknown'\n}\n\nfunction isBot(userAgent: string): boolean {\n  const botPatterns = [\n    /bot/i,\n    /crawler/i,\n    /spider/i,\n    /scraper/i,\n    /curl/i,\n    /wget/i,\n    /python/i,\n    /java/i,\n    /php/i\n  ]\n  \n  return botPatterns.some(pattern => pattern.test(userAgent))\n}\n\nfunction isAllowedBot(userAgent: string): boolean {\n  const allowedBots = [\n    /googlebot/i,\n    /bingbot/i,\n    /slurp/i, // Yahoo\n    /duckduckbot/i,\n    /baiduspider/i,\n    /yandexbot/i,\n    /facebookexternalhit/i,\n    /twitterbot/i,\n    /linkedinbot/i,\n    /whatsapp/i\n  ]\n  \n  return allowedBots.some(pattern => pattern.test(userAgent))\n}\n\nfunction isRateLimited(pathname: string, ip: string): boolean {\n  // Check auth endpoints\n  if (rateLimitedPaths.auth.some(path => pathname.startsWith(path))) {\n    return authRateLimit(ip)\n  }\n  \n  // Check API endpoints\n  if (rateLimitedPaths.api.some(path => pathname.startsWith(path))) {\n    return apiRateLimit(ip)\n  }\n  \n  // Check upload endpoints\n  if (rateLimitedPaths.upload.some(path => pathname.startsWith(path))) {\n    return uploadRateLimit(ip)\n  }\n  \n  // General rate limiting\n  return generalRateLimit(ip)\n}\n\nfunction isValidToken(token: string): boolean {\n  try {\n    // In a real app, verify JWT token here\n    // For demo, just check if token exists and has proper format\n    return token.length > 20 && token.includes('.')\n  } catch {\n    return false\n  }\n}\n\nfunction isAdminToken(token: string): boolean {\n  try {\n    // In a real app, decode JWT and check admin role\n    // For demo, just check for admin indicator\n    return token.includes('admin')\n  } catch {\n    return false\n  }\n}\n\nfunction isBlockedCountry(request: NextRequest): boolean {\n  // Get country from headers (set by CDN/proxy)\n  const country = request.headers.get('cf-ipcountry') || \n                 request.headers.get('x-country-code')\n  \n  // List of blocked countries (if any)\n  const blockedCountries: string[] = []\n  \n  return country ? blockedCountries.includes(country.toUpperCase()) : false\n}\n\nfunction generateRequestId(): string {\n  return Array.from(crypto.getRandomValues(new Uint8Array(16)))\n    .map(b => b.toString(16).padStart(2, '0'))\n    .join('')\n}\n\nfunction logSecurityEvent(request: NextRequest, ip: string, userAgent: string) {\n  const event = {\n    timestamp: new Date().toISOString(),\n    ip,\n    userAgent,\n    method: request.method,\n    pathname: request.nextUrl.pathname,\n    referer: request.headers.get('referer'),\n    origin: request.headers.get('origin')\n  }\n  \n  // In production, send to security monitoring service\n  console.log('Security event:', event)\n}\n\n// Honeypot endpoints to catch malicious bots\nfunction isHoneypotPath(pathname: string): boolean {\n  const honeypotPaths = [\n    '/admin.php',\n    '/wp-admin',\n    '/phpmyadmin',\n    '/.env',\n    '/config.php',\n    '/login.php'\n  ]\n  \n  return honeypotPaths.includes(pathname)\n}\n\n// Additional security checks\nfunction hasSecurityViolation(request: NextRequest): boolean {\n  const pathname = request.nextUrl.pathname\n  const query = request.nextUrl.search\n  \n  // Check for common attack patterns\n  const attackPatterns = [\n    /<script/i,\n    /javascript:/i,\n    /on\\w+=/i,\n    /union.*select/i,\n    /drop.*table/i,\n    /\\.\\.\\/\\.\\.\\//,\n    /etc\\/passwd/,\n    /cmd\\.exe/,\n    /powershell/i\n  ]\n  \n  const fullUrl = pathname + query\n  return attackPatterns.some(pattern => pattern.test(fullUrl))\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */\n    '/((?!_next/static|_next/image|favicon.ico|public/).*)',\n  ],\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAEA;;;AAEA,wCAAwC;AACxC,MAAM,mBAAmB,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,KAAK,MAAM,0BAA0B;;AACrF,MAAM,gBAAgB,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,EAAE,GAAG,KAAK,MAAM,6BAA6B;;AACnF,MAAM,eAAe,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,EAAE,IAAI,KAAK,MAAM,0BAA0B;;AAChF,MAAM,kBAAkB,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,EAAE,IAAI,KAAK,MAAM,wBAAwB;;AAEjF,mBAAmB;AACnB,MAAM,kBAAkB;IACtB,0BAA0B;IAC1B,2BAA2B,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD;IAE3C,uBAAuB;IACvB,mBAAmB;IAEnB,6BAA6B;IAC7B,0BAA0B;IAE1B,wBAAwB;IACxB,oBAAoB;IAEpB,kBAAkB;IAClB,mBAAmB;IAEnB,qBAAqB;IACrB,sBAAsB;IAEtB,wCAAwC;IACxC,6BAA6B;IAE7B,qCAAqC;IACrC,iBAAiB;IACjB,UAAU;IACV,WAAW;IAEX,wBAAwB;IACxB,gCAAgC;IAChC,8BAA8B;IAC9B,gCAAgC;AAClC;AAEA,oCAAoC;AACpC,MAAM,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;CACD;AAED,kCAAkC;AAClC,MAAM,aAAa;IACjB;CACD;AAED,oCAAoC;AACpC,MAAM,mBAAmB;IACvB,MAAM;QAAC;QAAoB;QAAoB;KAA2B;IAC1E,KAAK;QAAC;QAAiB;QAAc;KAAgB;IACrD,QAAQ;QAAC;KAAc;AACzB;AAEO,SAAS,WAAW,OAAoB;IAC7C,MAAM,WAAW,6LAAA,CAAA,eAAY,CAAC,IAAI;IAClC,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IACpC,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC,iBAAiB;IACvD,MAAM,KAAK,YAAY;IAEvB,wCAAwC;IACxC,OAAO,OAAO,CAAC,iBAAiB,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;QACnD,SAAS,OAAO,CAAC,GAAG,CAAC,KAAK;IAC5B;IAEA,6BAA6B;IAC7B,IAAI,MAAM,cAAc,CAAC,aAAa,YAAY;QAChD,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,UAAU,MAAM,EAAE,IAAI;QAClD,OAAO,IAAI,6LAAA,CAAA,eAAY,CAAC,iBAAiB;YAAE,QAAQ;QAAI;IACzD;IAEA,gBAAgB;IAChB,IAAI,cAAc,UAAU,KAAK;QAC/B,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,GAAG,IAAI,EAAE,UAAU;QAChD,OAAO,IAAI,6LAAA,CAAA,eAAY,CAAC,qBAAqB;YAC3C,QAAQ;YACR,SAAS;gBACP,eAAe;YACjB;QACF;IACF;IAEA,8CAA8C;IAC9C,IAAI;QAAC;QAAQ;QAAO;QAAU;KAAQ,CAAC,QAAQ,CAAC,QAAQ,MAAM,GAAG;QAC/D,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC;QACtC,MAAM,eAAe,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;QAExD,IAAI,CAAC,aAAa,CAAC,gBAAgB,cAAc,cAAc;YAC7D,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,GAAG,IAAI,EAAE,UAAU;YACvD,OAAO,IAAI,6LAAA,CAAA,eAAY,CAAC,uBAAuB;gBAAE,QAAQ;YAAI;QAC/D;IACF;IAEA,4CAA4C;IAC5C,IAAI,eAAe,IAAI,CAAC,CAAA,OAAQ,SAAS,UAAU,CAAC,QAAQ;QAC1D,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;QAEjD,IAAI,CAAC,SAAS,CAAC,aAAa,QAAQ;YAClC,MAAM,WAAW,IAAI,IAAI,gBAAgB,QAAQ,GAAG;YACpD,SAAS,YAAY,CAAC,GAAG,CAAC,YAAY;YACtC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B;IACF;IAEA,qBAAqB;IACrB,IAAI,WAAW,IAAI,CAAC,CAAA,OAAQ,SAAS,UAAU,CAAC,QAAQ;QACtD,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;QAEjD,IAAI,CAAC,SAAS,CAAC,aAAa,UAAU,CAAC,aAAa,QAAQ;YAC1D,OAAO,IAAI,6LAAA,CAAA,eAAY,CAAC,iBAAiB;gBAAE,QAAQ;YAAI;QACzD;IACF;IAEA,2BAA2B;IAC3B,IAAI,iBAAiB,UAAU;QAC7B,OAAO,IAAI,6LAAA,CAAA,eAAY,CAAC,wCAAwC;YAAE,QAAQ;QAAI;IAChF;IAEA,8BAA8B;IAC9B,MAAM,YAAY;IAClB,SAAS,OAAO,CAAC,GAAG,CAAC,gBAAgB;IAErC,sBAAsB;IACtB,iBAAiB,SAAS,IAAI;IAE9B,OAAO;AACT;AAEA,SAAS,YAAY,OAAoB;IACvC,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC;IACtC,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;IAEnC,IAAI,WAAW;QACb,OAAO,UAAU,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI;IACrC;IAEA,IAAI,QAAQ;QACV,OAAO;IACT;IAEA,OAAO,QAAQ,EAAE,IAAI;AACvB;AAEA,SAAS,MAAM,SAAiB;IAC9B,MAAM,cAAc;QAClB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,OAAO,YAAY,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC;AAClD;AAEA,SAAS,aAAa,SAAiB;IACrC,MAAM,cAAc;QAClB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,OAAO,YAAY,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC;AAClD;AAEA,SAAS,cAAc,QAAgB,EAAE,EAAU;IACjD,uBAAuB;IACvB,IAAI,iBAAiB,IAAI,CAAC,IAAI,CAAC,CAAA,OAAQ,SAAS,UAAU,CAAC,QAAQ;QACjE,OAAO,cAAc;IACvB;IAEA,sBAAsB;IACtB,IAAI,iBAAiB,GAAG,CAAC,IAAI,CAAC,CAAA,OAAQ,SAAS,UAAU,CAAC,QAAQ;QAChE,OAAO,aAAa;IACtB;IAEA,yBAAyB;IACzB,IAAI,iBAAiB,MAAM,CAAC,IAAI,CAAC,CAAA,OAAQ,SAAS,UAAU,CAAC,QAAQ;QACnE,OAAO,gBAAgB;IACzB;IAEA,wBAAwB;IACxB,OAAO,iBAAiB;AAC1B;AAEA,SAAS,aAAa,KAAa;IACjC,IAAI;QACF,uCAAuC;QACvC,6DAA6D;QAC7D,OAAO,MAAM,MAAM,GAAG,MAAM,MAAM,QAAQ,CAAC;IAC7C,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEA,SAAS,aAAa,KAAa;IACjC,IAAI;QACF,iDAAiD;QACjD,2CAA2C;QAC3C,OAAO,MAAM,QAAQ,CAAC;IACxB,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEA,SAAS,iBAAiB,OAAoB;IAC5C,8CAA8C;IAC9C,MAAM,UAAU,QAAQ,OAAO,CAAC,GAAG,CAAC,mBACrB,QAAQ,OAAO,CAAC,GAAG,CAAC;IAEnC,qCAAqC;IACrC,MAAM,mBAA6B,EAAE;IAErC,OAAO,UAAU,iBAAiB,QAAQ,CAAC,QAAQ,WAAW,MAAM;AACtE;AAEA,SAAS;IACP,OAAO,MAAM,IAAI,CAAC,OAAO,eAAe,CAAC,IAAI,WAAW,MACrD,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG,MACpC,IAAI,CAAC;AACV;AAEA,SAAS,iBAAiB,OAAoB,EAAE,EAAU,EAAE,SAAiB;IAC3E,MAAM,QAAQ;QACZ,WAAW,IAAI,OAAO,WAAW;QACjC;QACA;QACA,QAAQ,QAAQ,MAAM;QACtB,UAAU,QAAQ,OAAO,CAAC,QAAQ;QAClC,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;QAC7B,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC;IAC9B;IAEA,qDAAqD;IACrD,QAAQ,GAAG,CAAC,mBAAmB;AACjC;AAEA,6CAA6C;AAC7C,SAAS,eAAe,QAAgB;IACtC,MAAM,gBAAgB;QACpB;QACA;QACA;QACA;QACA;QACA;KACD;IAED,OAAO,cAAc,QAAQ,CAAC;AAChC;AAEA,6BAA6B;AAC7B,SAAS,qBAAqB,OAAoB;IAChD,MAAM,WAAW,QAAQ,OAAO,CAAC,QAAQ;IACzC,MAAM,QAAQ,QAAQ,OAAO,CAAC,MAAM;IAEpC,mCAAmC;IACnC,MAAM,iBAAiB;QACrB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,UAAU,WAAW;IAC3B,OAAO,eAAe,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC;AACrD;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}