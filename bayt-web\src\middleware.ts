import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { generateCSPHeader, createRateLimiter } from '@/lib/validation'

// Rate limiters for different endpoints
const generalRateLimit = createRateLimiter(100, 60 * 1000) // 100 requests per minute
const authRateLimit = createRateLimiter(5, 60 * 1000) // 5 auth attempts per minute
const apiRateLimit = createRateLimiter(50, 60 * 1000) // 50 API calls per minute
const uploadRateLimit = createRateLimiter(10, 60 * 1000) // 10 uploads per minute

// Security headers
const securityHeaders = {
  // Content Security Policy
  'Content-Security-Policy': generateCSPHeader(),
  
  // Prevent clickjacking
  'X-Frame-Options': 'DENY',
  
  // Prevent MIME type sniffing
  'X-Content-Type-Options': 'nosniff',
  
  // Enable XSS protection
  'X-XSS-Protection': '1; mode=block',
  
  // Referrer policy
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  
  // Permissions policy
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=(self), payment=()',
  
  // HSTS (HTTP Strict Transport Security)
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
  
  // Prevent caching of sensitive pages
  'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
  'Pragma': 'no-cache',
  'Expires': '0',
  
  // Cross-Origin policies
  'Cross-Origin-Embedder-Policy': 'require-corp',
  'Cross-Origin-Opener-Policy': 'same-origin',
  'Cross-Origin-Resource-Policy': 'same-origin'
}

// Paths that require authentication
const protectedPaths = [
  '/dashboard',
  '/profile',
  '/messages',
  '/favorites',
  '/sell',
  '/settings'
]

// Paths that require admin access
const adminPaths = [
  '/admin'
]

// API paths that need rate limiting
const rateLimitedPaths = {
  auth: ['/api/auth/signin', '/api/auth/signup', '/api/auth/reset-password'],
  api: ['/api/products', '/api/users', '/api/messages'],
  upload: ['/api/upload']
}

export function middleware(request: NextRequest) {
  const response = NextResponse.next()
  const { pathname } = request.nextUrl
  const userAgent = request.headers.get('user-agent') || ''
  const ip = getClientIP(request)

  // Add security headers to all responses
  Object.entries(securityHeaders).forEach(([key, value]) => {
    response.headers.set(key, value)
  })

  // Bot detection and blocking
  if (isBot(userAgent) && !isAllowedBot(userAgent)) {
    console.log(`Blocked bot: ${userAgent} from ${ip}`)
    return new NextResponse('Access Denied', { status: 403 })
  }

  // Rate limiting
  if (isRateLimited(pathname, ip)) {
    console.log(`Rate limited: ${ip} on ${pathname}`)
    return new NextResponse('Too Many Requests', { 
      status: 429,
      headers: {
        'Retry-After': '60'
      }
    })
  }

  // CSRF protection for state-changing requests
  if (['POST', 'PUT', 'DELETE', 'PATCH'].includes(request.method)) {
    const csrfToken = request.headers.get('x-csrf-token')
    const sessionToken = request.cookies.get('csrf-token')?.value

    if (!csrfToken || !sessionToken || csrfToken !== sessionToken) {
      console.log(`CSRF token mismatch: ${ip} on ${pathname}`)
      return new NextResponse('CSRF Token Mismatch', { status: 403 })
    }
  }

  // Authentication check for protected routes
  if (protectedPaths.some(path => pathname.startsWith(path))) {
    const token = request.cookies.get('auth-token')?.value
    
    if (!token || !isValidToken(token)) {
      const loginUrl = new URL('/auth/signin', request.url)
      loginUrl.searchParams.set('redirect', pathname)
      return NextResponse.redirect(loginUrl)
    }
  }

  // Admin access check
  if (adminPaths.some(path => pathname.startsWith(path))) {
    const token = request.cookies.get('auth-token')?.value
    
    if (!token || !isValidToken(token) || !isAdminToken(token)) {
      return new NextResponse('Access Denied', { status: 403 })
    }
  }

  // Geo-blocking (if needed)
  if (isBlockedCountry(request)) {
    return new NextResponse('Service not available in your region', { status: 451 })
  }

  // Add request ID for tracking
  const requestId = generateRequestId()
  response.headers.set('X-Request-ID', requestId)

  // Log security events
  logSecurityEvent(request, ip, userAgent)

  return response
}

function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  if (realIP) {
    return realIP
  }
  
  return request.ip || 'unknown'
}

function isBot(userAgent: string): boolean {
  const botPatterns = [
    /bot/i,
    /crawler/i,
    /spider/i,
    /scraper/i,
    /curl/i,
    /wget/i,
    /python/i,
    /java/i,
    /php/i
  ]
  
  return botPatterns.some(pattern => pattern.test(userAgent))
}

function isAllowedBot(userAgent: string): boolean {
  const allowedBots = [
    /googlebot/i,
    /bingbot/i,
    /slurp/i, // Yahoo
    /duckduckbot/i,
    /baiduspider/i,
    /yandexbot/i,
    /facebookexternalhit/i,
    /twitterbot/i,
    /linkedinbot/i,
    /whatsapp/i
  ]
  
  return allowedBots.some(pattern => pattern.test(userAgent))
}

function isRateLimited(pathname: string, ip: string): boolean {
  // Check auth endpoints
  if (rateLimitedPaths.auth.some(path => pathname.startsWith(path))) {
    return authRateLimit(ip)
  }
  
  // Check API endpoints
  if (rateLimitedPaths.api.some(path => pathname.startsWith(path))) {
    return apiRateLimit(ip)
  }
  
  // Check upload endpoints
  if (rateLimitedPaths.upload.some(path => pathname.startsWith(path))) {
    return uploadRateLimit(ip)
  }
  
  // General rate limiting
  return generalRateLimit(ip)
}

function isValidToken(token: string): boolean {
  try {
    // In a real app, verify JWT token here
    // For demo, just check if token exists and has proper format
    return token.length > 20 && token.includes('.')
  } catch {
    return false
  }
}

function isAdminToken(token: string): boolean {
  try {
    // In a real app, decode JWT and check admin role
    // For demo, just check for admin indicator
    return token.includes('admin')
  } catch {
    return false
  }
}

function isBlockedCountry(request: NextRequest): boolean {
  // Get country from headers (set by CDN/proxy)
  const country = request.headers.get('cf-ipcountry') || 
                 request.headers.get('x-country-code')
  
  // List of blocked countries (if any)
  const blockedCountries: string[] = []
  
  return country ? blockedCountries.includes(country.toUpperCase()) : false
}

function generateRequestId(): string {
  return Array.from(crypto.getRandomValues(new Uint8Array(16)))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('')
}

function logSecurityEvent(request: NextRequest, ip: string, userAgent: string) {
  const event = {
    timestamp: new Date().toISOString(),
    ip,
    userAgent,
    method: request.method,
    pathname: request.nextUrl.pathname,
    referer: request.headers.get('referer'),
    origin: request.headers.get('origin')
  }
  
  // In production, send to security monitoring service
  console.log('Security event:', event)
}

// Honeypot endpoints to catch malicious bots
function isHoneypotPath(pathname: string): boolean {
  const honeypotPaths = [
    '/admin.php',
    '/wp-admin',
    '/phpmyadmin',
    '/.env',
    '/config.php',
    '/login.php'
  ]
  
  return honeypotPaths.includes(pathname)
}

// Additional security checks
function hasSecurityViolation(request: NextRequest): boolean {
  const pathname = request.nextUrl.pathname
  const query = request.nextUrl.search
  
  // Check for common attack patterns
  const attackPatterns = [
    /<script/i,
    /javascript:/i,
    /on\w+=/i,
    /union.*select/i,
    /drop.*table/i,
    /\.\.\/\.\.\//,
    /etc\/passwd/,
    /cmd\.exe/,
    /powershell/i
  ]
  
  const fullUrl = pathname + query
  return attackPatterns.some(pattern => pattern.test(fullUrl))
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
}
