'use client'

import { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Search, 
  Filter, 
  MapPin, 
  DollarSign, 
  Calendar,
  Sparkles,
  Mic,
  Camera,
  X,
  TrendingUp,
  Clock,
  Star
} from 'lucide-react'
import { But<PERSON> } from '@/components/ui/Button'
import { useDebouncedSearch } from '@/components/ui/LazyComponents'

interface SearchFilters {
  query: string
  location: string
  category: string
  priceMin: number
  priceMax: number
  condition: string
  dateRange: string
  sortBy: string
}

interface SmartSuggestion {
  id: string
  text: string
  type: 'product' | 'category' | 'location' | 'trending'
  confidence: number
  metadata?: any
}

export function SmartSearch({ onSearch }: { onSearch: (filters: SearchFilters) => void }) {
  const [filters, setFilters] = useState<SearchFilters>({
    query: '',
    location: '',
    category: '',
    priceMin: 0,
    priceMax: 0,
    condition: '',
    dateRange: '',
    sortBy: 'relevance'
  })

  const [isAdvancedOpen, setIsAdvancedOpen] = useState(false)
  const [suggestions, setSuggestions] = useState<SmartSuggestion[]>([])
  const [isListening, setIsListening] = useState(false)
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [recentSearches, setRecentSearches] = useState<string[]>([])
  const [trendingSearches, setTrendingSearches] = useState<string[]>([
    'iPhone 15', 'BMW X5', 'Damascus apartment', 'Gaming laptop', 'Wedding dress'
  ])

  const debouncedQuery = useDebouncedSearch(filters.query, 300)
  const searchRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (debouncedQuery.length > 2) {
      generateSmartSuggestions(debouncedQuery)
    } else {
      setSuggestions([])
    }
  }, [debouncedQuery])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowSuggestions(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const generateSmartSuggestions = async (query: string) => {
    // Simulate AI-powered suggestions
    const mockSuggestions: SmartSuggestion[] = [
      {
        id: '1',
        text: `${query} in Damascus`,
        type: 'location',
        confidence: 0.9,
        metadata: { location: 'Damascus' }
      },
      {
        id: '2',
        text: `Used ${query}`,
        type: 'product',
        confidence: 0.8,
        metadata: { condition: 'used' }
      },
      {
        id: '3',
        text: `New ${query}`,
        type: 'product',
        confidence: 0.8,
        metadata: { condition: 'new' }
      },
      {
        id: '4',
        text: `${query} under $500`,
        type: 'product',
        confidence: 0.7,
        metadata: { priceMax: 500 }
      }
    ]

    setSuggestions(mockSuggestions)
    setShowSuggestions(true)
  }

  const handleVoiceSearch = () => {
    if (!('webkitSpeechRecognition' in window)) {
      alert('Voice search is not supported in your browser')
      return
    }

    const recognition = new (window as any).webkitSpeechRecognition()
    recognition.continuous = false
    recognition.interimResults = false
    recognition.lang = 'en-US'

    setIsListening(true)

    recognition.onresult = (event: any) => {
      const transcript = event.results[0][0].transcript
      setFilters(prev => ({ ...prev, query: transcript }))
      setIsListening(false)
    }

    recognition.onerror = () => {
      setIsListening(false)
    }

    recognition.onend = () => {
      setIsListening(false)
    }

    recognition.start()
  }

  const handleImageSearch = () => {
    // Implement image search functionality
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = 'image/*'
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (file) {
        // Process image for search
        console.log('Image search:', file)
      }
    }
    input.click()
  }

  const handleSuggestionClick = (suggestion: SmartSuggestion) => {
    const newFilters = { ...filters }
    
    if (suggestion.metadata) {
      Object.assign(newFilters, suggestion.metadata)
    }
    
    newFilters.query = suggestion.text
    setFilters(newFilters)
    setShowSuggestions(false)
    
    // Add to recent searches
    const updatedRecent = [suggestion.text, ...recentSearches.filter(s => s !== suggestion.text)].slice(0, 5)
    setRecentSearches(updatedRecent)
    localStorage.setItem('recentSearches', JSON.stringify(updatedRecent))
  }

  const handleSearch = () => {
    onSearch(filters)
    setShowSuggestions(false)
    
    // Add to recent searches
    if (filters.query) {
      const updatedRecent = [filters.query, ...recentSearches.filter(s => s !== filters.query)].slice(0, 5)
      setRecentSearches(updatedRecent)
      localStorage.setItem('recentSearches', JSON.stringify(updatedRecent))
    }
  }

  const clearFilters = () => {
    setFilters({
      query: '',
      location: '',
      category: '',
      priceMin: 0,
      priceMax: 0,
      condition: '',
      dateRange: '',
      sortBy: 'relevance'
    })
  }

  return (
    <div ref={searchRef} className="relative">
      {/* Main Search Bar */}
      <motion.div 
        className="bg-white/90 backdrop-blur-lg rounded-2xl shadow-xl border border-white/20 p-6"
        whileHover={{ scale: 1.02 }}
        transition={{ duration: 0.2 }}
      >
        <div className="flex items-center gap-4">
          {/* Search Input */}
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search for anything..."
              value={filters.query}
              onChange={(e) => setFilters(prev => ({ ...prev, query: e.target.value }))}
              onFocus={() => setShowSuggestions(true)}
              className="w-full pl-10 pr-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
            />
            
            {/* Voice Search */}
            <motion.button
              onClick={handleVoiceSearch}
              className={`absolute right-12 top-1/2 transform -translate-y-1/2 p-2 rounded-lg transition-colors ${
                isListening ? 'bg-red-100 text-red-600' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <Mic className={`w-4 h-4 ${isListening ? 'animate-pulse' : ''}`} />
            </motion.button>

            {/* Image Search */}
            <motion.button
              onClick={handleImageSearch}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 p-2 bg-gray-100 text-gray-600 rounded-lg hover:bg-gray-200 transition-colors"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <Camera className="w-4 h-4" />
            </motion.button>
          </div>

          {/* Location Input */}
          <div className="relative">
            <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Location"
              value={filters.location}
              onChange={(e) => setFilters(prev => ({ ...prev, location: e.target.value }))}
              className="w-48 pl-10 pr-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
            />
          </div>

          {/* Advanced Filters Toggle */}
          <motion.button
            onClick={() => setIsAdvancedOpen(!isAdvancedOpen)}
            className={`p-3 rounded-xl border-2 transition-all duration-300 ${
              isAdvancedOpen 
                ? 'bg-blue-500 text-white border-blue-500' 
                : 'bg-white text-gray-600 border-gray-200 hover:border-blue-300'
            }`}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Filter className="w-5 h-5" />
          </motion.button>

          {/* Search Button */}
          <Button
            onClick={handleSearch}
            className="px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
          >
            <Search className="w-5 h-5 mr-2" />
            Search
          </Button>
        </div>

        {/* Smart Suggestions */}
        <AnimatePresence>
          {showSuggestions && (suggestions.length > 0 || recentSearches.length > 0 || trendingSearches.length > 0) && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 10 }}
              className="absolute top-full left-0 right-0 mt-2 bg-white rounded-2xl shadow-2xl border border-gray-100 z-50 max-h-96 overflow-y-auto"
            >
              {/* AI Suggestions */}
              {suggestions.length > 0 && (
                <div className="p-4 border-b border-gray-100">
                  <div className="flex items-center gap-2 mb-3">
                    <Sparkles className="w-4 h-4 text-purple-500" />
                    <span className="text-sm font-medium text-gray-700">Smart Suggestions</span>
                  </div>
                  {suggestions.map((suggestion) => (
                    <motion.button
                      key={suggestion.id}
                      onClick={() => handleSuggestionClick(suggestion)}
                      className="w-full text-left p-2 rounded-lg hover:bg-gray-50 transition-colors"
                      whileHover={{ x: 4 }}
                    >
                      <div className="flex items-center justify-between">
                        <span className="text-gray-900">{suggestion.text}</span>
                        <div className="flex items-center gap-1">
                          <div className={`w-2 h-2 rounded-full ${
                            suggestion.confidence > 0.8 ? 'bg-green-400' :
                            suggestion.confidence > 0.6 ? 'bg-yellow-400' : 'bg-gray-400'
                          }`} />
                          <span className="text-xs text-gray-500">{Math.round(suggestion.confidence * 100)}%</span>
                        </div>
                      </div>
                    </motion.button>
                  ))}
                </div>
              )}

              {/* Recent Searches */}
              {recentSearches.length > 0 && (
                <div className="p-4 border-b border-gray-100">
                  <div className="flex items-center gap-2 mb-3">
                    <Clock className="w-4 h-4 text-gray-500" />
                    <span className="text-sm font-medium text-gray-700">Recent Searches</span>
                  </div>
                  {recentSearches.map((search, index) => (
                    <motion.button
                      key={index}
                      onClick={() => setFilters(prev => ({ ...prev, query: search }))}
                      className="w-full text-left p-2 rounded-lg hover:bg-gray-50 transition-colors text-gray-600"
                      whileHover={{ x: 4 }}
                    >
                      {search}
                    </motion.button>
                  ))}
                </div>
              )}

              {/* Trending Searches */}
              {trendingSearches.length > 0 && (
                <div className="p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <TrendingUp className="w-4 h-4 text-orange-500" />
                    <span className="text-sm font-medium text-gray-700">Trending Now</span>
                  </div>
                  {trendingSearches.map((search, index) => (
                    <motion.button
                      key={index}
                      onClick={() => setFilters(prev => ({ ...prev, query: search }))}
                      className="w-full text-left p-2 rounded-lg hover:bg-gray-50 transition-colors text-gray-600"
                      whileHover={{ x: 4 }}
                    >
                      <div className="flex items-center gap-2">
                        <span className="text-xs bg-orange-100 text-orange-600 px-2 py-1 rounded-full">#{index + 1}</span>
                        {search}
                      </div>
                    </motion.button>
                  ))}
                </div>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>

      {/* Advanced Filters */}
      <AnimatePresence>
        {isAdvancedOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mt-4 bg-white/90 backdrop-blur-lg rounded-2xl shadow-xl border border-white/20 p-6"
          >
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Advanced Filters</h3>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearFilters}
                  className="text-gray-600"
                >
                  Clear All
                </Button>
                <motion.button
                  onClick={() => setIsAdvancedOpen(false)}
                  className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <X className="w-5 h-5" />
                </motion.button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* Category Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
                <select
                  value={filters.category}
                  onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value }))}
                  className="w-full p-3 bg-gray-50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">All Categories</option>
                  <option value="electronics">Electronics</option>
                  <option value="vehicles">Vehicles</option>
                  <option value="real-estate">Real Estate</option>
                  <option value="fashion">Fashion & Beauty</option>
                  <option value="home">Home & Garden</option>
                  <option value="sports">Sports & Hobbies</option>
                </select>
              </div>

              {/* Price Range */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Price Range</label>
                <div className="flex gap-2">
                  <input
                    type="number"
                    placeholder="Min"
                    value={filters.priceMin || ''}
                    onChange={(e) => setFilters(prev => ({ ...prev, priceMin: Number(e.target.value) }))}
                    className="w-full p-3 bg-gray-50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <input
                    type="number"
                    placeholder="Max"
                    value={filters.priceMax || ''}
                    onChange={(e) => setFilters(prev => ({ ...prev, priceMax: Number(e.target.value) }))}
                    className="w-full p-3 bg-gray-50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Condition */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Condition</label>
                <select
                  value={filters.condition}
                  onChange={(e) => setFilters(prev => ({ ...prev, condition: e.target.value }))}
                  className="w-full p-3 bg-gray-50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Any Condition</option>
                  <option value="new">New</option>
                  <option value="like_new">Like New</option>
                  <option value="good">Good</option>
                  <option value="fair">Fair</option>
                  <option value="poor">Poor</option>
                </select>
              </div>

              {/* Sort By */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
                <select
                  value={filters.sortBy}
                  onChange={(e) => setFilters(prev => ({ ...prev, sortBy: e.target.value }))}
                  className="w-full p-3 bg-gray-50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="relevance">Relevance</option>
                  <option value="price_low">Price: Low to High</option>
                  <option value="price_high">Price: High to Low</option>
                  <option value="newest">Newest First</option>
                  <option value="oldest">Oldest First</option>
                  <option value="distance">Distance</option>
                  <option value="popularity">Most Popular</option>
                </select>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
