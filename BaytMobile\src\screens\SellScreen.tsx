import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';

const SellScreen = ({ navigation }: any) => {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Sell Your Item</Text>
      </View>
      
      <View style={styles.content}>
        <View style={styles.iconContainer}>
          <Icon name="add-circle" size={80} color="#2563eb" />
        </View>
        
        <Text style={styles.title}>Start Selling</Text>
        <Text style={styles.subtitle}>
          List your items and reach thousands of potential buyers
        </Text>
        
        <TouchableOpacity style={styles.sellButton}>
          <Icon name="camera-alt" size={24} color="#fff" style={styles.buttonIcon} />
          <Text style={styles.sellButtonText}>Add Photos & Details</Text>
        </TouchableOpacity>
        
        <View style={styles.features}>
          <View style={styles.feature}>
            <Icon name="photo-camera" size={24} color="#2563eb" />
            <Text style={styles.featureText}>Add up to 10 photos</Text>
          </View>
          <View style={styles.feature}>
            <Icon name="location-on" size={24} color="#2563eb" />
            <Text style={styles.featureText}>Set your location</Text>
          </View>
          <View style={styles.feature}>
            <Icon name="price-check" size={24} color="#2563eb" />
            <Text style={styles.featureText}>Set your price</Text>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#111827',
    textAlign: 'center',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconContainer: {
    marginBottom: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 24,
  },
  sellButton: {
    backgroundColor: '#2563eb',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderRadius: 12,
    marginBottom: 32,
  },
  buttonIcon: {
    marginRight: 8,
  },
  sellButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
  },
  features: {
    width: '100%',
  },
  feature: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
  },
  featureText: {
    fontSize: 16,
    color: '#374151',
    marginLeft: 16,
  },
});

export default SellScreen;
