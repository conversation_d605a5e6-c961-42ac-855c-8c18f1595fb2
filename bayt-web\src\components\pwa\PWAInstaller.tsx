'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Download, X, Smartphone, Monitor, Share, Plus } from 'lucide-react'
import { Button } from '@/components/ui/Button'

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[]
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed'
    platform: string
  }>
  prompt(): Promise<void>
}

export function PWAInstaller() {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null)
  const [showInstallPrompt, setShowInstallPrompt] = useState(false)
  const [isInstalled, setIsInstalled] = useState(false)
  const [isIOS, setIsIOS] = useState(false)
  const [isStandalone, setIsStandalone] = useState(false)

  useEffect(() => {
    // Check if app is already installed
    setIsStandalone(window.matchMedia('(display-mode: standalone)').matches)
    
    // Check if iOS
    setIsIOS(/iPad|iPhone|iPod/.test(navigator.userAgent))
    
    // Check if already installed
    if ('getInstalledRelatedApps' in navigator) {
      (navigator as any).getInstalledRelatedApps().then((relatedApps: any[]) => {
        setIsInstalled(relatedApps.length > 0)
      })
    }

    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault()
      setDeferredPrompt(e as BeforeInstallPromptEvent)
      
      // Show install prompt after a delay
      setTimeout(() => {
        if (!isStandalone && !isInstalled) {
          setShowInstallPrompt(true)
        }
      }, 5000)
    }

    const handleAppInstalled = () => {
      setIsInstalled(true)
      setShowInstallPrompt(false)
      setDeferredPrompt(null)
    }

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    window.addEventListener('appinstalled', handleAppInstalled)

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
      window.removeEventListener('appinstalled', handleAppInstalled)
    }
  }, [isStandalone, isInstalled])

  const handleInstallClick = async () => {
    if (!deferredPrompt) return

    deferredPrompt.prompt()
    const { outcome } = await deferredPrompt.userChoice
    
    if (outcome === 'accepted') {
      console.log('User accepted the install prompt')
    } else {
      console.log('User dismissed the install prompt')
    }
    
    setDeferredPrompt(null)
    setShowInstallPrompt(false)
  }

  const handleDismiss = () => {
    setShowInstallPrompt(false)
    // Don't show again for 24 hours
    localStorage.setItem('pwa-install-dismissed', Date.now().toString())
  }

  // Don't show if already installed or dismissed recently
  if (isInstalled || isStandalone) return null

  const dismissedTime = localStorage.getItem('pwa-install-dismissed')
  if (dismissedTime && Date.now() - parseInt(dismissedTime) < 24 * 60 * 60 * 1000) {
    return null
  }

  return (
    <AnimatePresence>
      {showInstallPrompt && (
        <motion.div
          initial={{ opacity: 0, y: 100 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 100 }}
          className="fixed bottom-4 left-4 right-4 z-50 md:left-auto md:right-4 md:w-96"
        >
          <div className="bg-white rounded-2xl shadow-2xl border border-gray-200 p-6">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center">
                  <Smartphone className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">Install Bayt App</h3>
                  <p className="text-sm text-gray-600">Get the full experience</p>
                </div>
              </div>
              <motion.button
                onClick={handleDismiss}
                className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <X className="w-5 h-5" />
              </motion.button>
            </div>

            <div className="space-y-3 mb-6">
              <div className="flex items-center gap-3 text-sm text-gray-600">
                <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center">
                  <div className="w-2 h-2 bg-green-500 rounded-full" />
                </div>
                <span>Works offline</span>
              </div>
              <div className="flex items-center gap-3 text-sm text-gray-600">
                <div className="w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center">
                  <div className="w-2 h-2 bg-blue-500 rounded-full" />
                </div>
                <span>Fast and reliable</span>
              </div>
              <div className="flex items-center gap-3 text-sm text-gray-600">
                <div className="w-5 h-5 bg-purple-100 rounded-full flex items-center justify-center">
                  <div className="w-2 h-2 bg-purple-500 rounded-full" />
                </div>
                <span>Native app experience</span>
              </div>
            </div>

            {isIOS ? (
              <IOSInstallInstructions />
            ) : (
              <div className="flex gap-3">
                <Button
                  onClick={handleInstallClick}
                  className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                  disabled={!deferredPrompt}
                >
                  <Download className="w-4 h-4 mr-2" />
                  Install App
                </Button>
                <Button
                  onClick={handleDismiss}
                  variant="outline"
                  className="px-4"
                >
                  Later
                </Button>
              </div>
            )}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

function IOSInstallInstructions() {
  const [step, setStep] = useState(0)

  const steps = [
    {
      icon: Share,
      text: 'Tap the Share button',
      detail: 'Look for the share icon in your browser'
    },
    {
      icon: Plus,
      text: 'Add to Home Screen',
      detail: 'Scroll down and tap "Add to Home Screen"'
    },
    {
      icon: Download,
      text: 'Confirm Installation',
      detail: 'Tap "Add" to install the app'
    }
  ]

  return (
    <div className="space-y-4">
      <p className="text-sm text-gray-600 mb-4">
        To install this app on your iPhone:
      </p>
      
      <div className="space-y-3">
        {steps.map((stepItem, index) => {
          const Icon = stepItem.icon
          return (
            <motion.div
              key={index}
              className={`flex items-center gap-3 p-3 rounded-lg transition-colors ${
                index === step ? 'bg-blue-50 border border-blue-200' : 'bg-gray-50'
              }`}
              animate={{ scale: index === step ? 1.02 : 1 }}
            >
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                index === step ? 'bg-blue-500 text-white' : 'bg-gray-300 text-gray-600'
              }`}>
                <Icon className="w-4 h-4" />
              </div>
              <div>
                <div className="font-medium text-sm text-gray-900">{stepItem.text}</div>
                <div className="text-xs text-gray-600">{stepItem.detail}</div>
              </div>
            </motion.div>
          )
        })}
      </div>

      <div className="flex gap-2 mt-4">
        <Button
          onClick={() => setStep(Math.max(0, step - 1))}
          variant="outline"
          size="sm"
          disabled={step === 0}
          className="flex-1"
        >
          Previous
        </Button>
        <Button
          onClick={() => setStep(Math.min(steps.length - 1, step + 1))}
          size="sm"
          disabled={step === steps.length - 1}
          className="flex-1"
        >
          Next
        </Button>
      </div>
    </div>
  )
}

// PWA status indicator
export function PWAStatus() {
  const [isOnline, setIsOnline] = useState(true)
  const [isStandalone, setIsStandalone] = useState(false)
  const [updateAvailable, setUpdateAvailable] = useState(false)

  useEffect(() => {
    setIsOnline(navigator.onLine)
    setIsStandalone(window.matchMedia('(display-mode: standalone)').matches)

    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    // Check for service worker updates
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        setUpdateAvailable(true)
      })
    }

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  const handleUpdate = () => {
    window.location.reload()
  }

  if (!isStandalone) return null

  return (
    <div className="fixed top-0 left-0 right-0 z-50">
      <AnimatePresence>
        {!isOnline && (
          <motion.div
            initial={{ y: -100 }}
            animate={{ y: 0 }}
            exit={{ y: -100 }}
            className="bg-yellow-500 text-white text-center py-2 px-4 text-sm"
          >
            You're offline. Some features may not work.
          </motion.div>
        )}
        
        {updateAvailable && (
          <motion.div
            initial={{ y: -100 }}
            animate={{ y: 0 }}
            exit={{ y: -100 }}
            className="bg-blue-500 text-white text-center py-2 px-4 text-sm"
          >
            <span>New version available! </span>
            <button
              onClick={handleUpdate}
              className="underline font-medium hover:no-underline"
            >
              Update now
            </button>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

// Hook for PWA features
export function usePWA() {
  const [isInstalled, setIsInstalled] = useState(false)
  const [isStandalone, setIsStandalone] = useState(false)
  const [isOnline, setIsOnline] = useState(true)
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null)

  useEffect(() => {
    setIsStandalone(window.matchMedia('(display-mode: standalone)').matches)
    setIsOnline(navigator.onLine)

    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault()
      setDeferredPrompt(e as BeforeInstallPromptEvent)
    }

    const handleAppInstalled = () => {
      setIsInstalled(true)
      setDeferredPrompt(null)
    }

    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    window.addEventListener('appinstalled', handleAppInstalled)
    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
      window.removeEventListener('appinstalled', handleAppInstalled)
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  const installApp = async () => {
    if (!deferredPrompt) return false

    deferredPrompt.prompt()
    const { outcome } = await deferredPrompt.userChoice
    
    setDeferredPrompt(null)
    return outcome === 'accepted'
  }

  return {
    isInstalled,
    isStandalone,
    isOnline,
    canInstall: !!deferredPrompt,
    installApp
  }
}
