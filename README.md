# Bayt - Syrian Marketplace Platform

Bayt is a comprehensive marketplace platform designed specifically for Syria, enabling users to buy and sell various items with confidence. The platform supports both web and mobile applications with multi-language support (Arabic, Turkish, and English).

## 🌟 Features

### Core Features
- **User Authentication**: Secure sign-up/sign-in with email verification
- **Product Listings**: Create, edit, and manage product listings with multiple photos
- **Advanced Search**: Filter by category, price, location, and condition
- **Real-time Messaging**: Direct communication between buyers and sellers
- **Favorites System**: Save and manage favorite products
- **User Profiles**: Comprehensive user profiles with ratings and reviews
- **Multi-language Support**: Arabic, Turkish, and English interfaces

### Advanced Features
- **Location-based Search**: Find products near your location
- **Push Notifications**: Stay updated with important activities
- **Social Sharing**: Share products on social media platforms
- **Review System**: Rate and review other users
- **Category Management**: Organized product categories and subcategories
- **Image Upload**: Multiple image support with cloud storage
- **Responsive Design**: Works seamlessly on all devices

## 🏗️ Architecture

### Web Application (Next.js)
- **Framework**: Next.js 14 with App Router
- **Styling**: Tailwind CSS
- **Authentication**: Supabase Auth
- **Database**: PostgreSQL with Supabase
- **Storage**: Supabase Storage for images
- **State Management**: React Context API
- **Form Handling**: React Hook Form with Zod validation

### Mobile Application (React Native)
- **Framework**: React Native
- **Navigation**: React Navigation
- **Authentication**: Supabase Auth
- **Platform**: Android (iOS support can be added)

### Database Schema
- **Users**: User profiles and authentication data
- **Products**: Product listings with images and metadata
- **Categories**: Hierarchical category structure
- **Messages**: Real-time messaging between users
- **Favorites**: User's saved products
- **Reviews**: User ratings and reviews
- **Notifications**: System and user notifications

## 📁 Project Structure

```
Bayt/
├── bayt-web/                 # Next.js Web Application
│   ├── src/
│   │   ├── app/             # App Router pages
│   │   ├── components/      # Reusable components
│   │   ├── contexts/        # React contexts
│   │   ├── lib/            # Utilities and configurations
│   │   └── types/          # TypeScript type definitions
│   ├── public/             # Static assets
│   └── package.json
├── BaytMobile/              # React Native Mobile App
│   ├── src/
│   │   ├── screens/        # Mobile screens
│   │   ├── components/     # Mobile components
│   │   └── contexts/       # Shared contexts
│   └── package.json
└── database/               # Database schema and functions
    ├── schema.sql          # Database tables
    ├── functions.sql       # Stored procedures
    ├── security.sql        # RLS policies
    └── seed_data.sql       # Initial data
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Supabase account
- Android Studio (for mobile development)

### Web Application Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd Bayt/bayt-web
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Configuration**
   Create `.env.local` file:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
   NEXT_PUBLIC_APP_URL=http://localhost:3000
   ```

4. **Database Setup**
   - Create a new Supabase project
   - Run the SQL files in order:
     ```sql
     -- Run these in Supabase SQL Editor
     database/schema.sql
     database/functions.sql
     database/security.sql
     database/seed_data.sql
     ```

5. **Start Development Server**
   ```bash
   npm run dev
   ```

### Mobile Application Setup

1. **Navigate to mobile directory**
   ```bash
   cd ../BaytMobile
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Update Supabase Configuration**
   Edit `src/contexts/AuthContext.tsx` with your Supabase credentials

4. **Run on Android**
   ```bash
   npx react-native run-android
   ```

## 🗄️ Database Schema

### Main Tables

- **users**: User profiles and authentication
- **categories**: Product categories (hierarchical)
- **products**: Product listings with images
- **messages**: Real-time messaging
- **favorites**: User's saved products
- **reviews**: User ratings and reviews
- **notifications**: System notifications

### Key Features

- **Row Level Security (RLS)**: Secure data access
- **Real-time subscriptions**: Live updates
- **Full-text search**: Advanced product search
- **Geospatial queries**: Location-based features
- **Automated triggers**: Data consistency

## 🌐 Multi-language Support

The platform supports three languages:
- **English** (en): Default language
- **Arabic** (ar): Right-to-left support
- **Turkish** (tr): Full localization

Language files are located in `src/lib/i18n.ts` with comprehensive translations for all UI elements.

## 📱 Mobile Features

- **Native Navigation**: Smooth tab and stack navigation
- **Image Picker**: Camera and gallery integration
- **Push Notifications**: Real-time alerts
- **Offline Support**: Basic offline functionality
- **Responsive Design**: Optimized for all screen sizes

## 🔐 Security Features

- **Authentication**: Secure email/password authentication
- **Authorization**: Role-based access control
- **Data Validation**: Client and server-side validation
- **SQL Injection Protection**: Parameterized queries
- **XSS Protection**: Input sanitization
- **CSRF Protection**: Built-in Next.js protection

## 🚀 Deployment

### Web Application
- **Vercel**: Recommended for Next.js deployment
- **Netlify**: Alternative deployment option
- **Docker**: Containerized deployment

### Mobile Application
- **Google Play Store**: Android app distribution
- **APK Distribution**: Direct APK sharing

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation

## 🔮 Future Enhancements

- **Payment Integration**: Secure payment processing
- **Video Calls**: In-app video communication
- **AI Recommendations**: Smart product suggestions
- **Advanced Analytics**: User behavior insights
- **Multi-vendor Support**: Business accounts
- **Auction System**: Bidding functionality

---

**Bayt** - Connecting Syria through trusted marketplace experiences.
