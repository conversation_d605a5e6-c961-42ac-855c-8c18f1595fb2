/**
 * @fileoverview Common utils for directives.
 *
 * This file contains only shared items for directives.
 * If you make a utility for rules, please see `../rules/utils/ast-utils.js`.
 *
 * <AUTHOR> <https://github.com/gfyoung>
 */
"use strict";

const directivesPattern =
	/^(eslint(?:-env|-enable|-disable(?:(?:-next)?-line)?)?|exported|globals?)(?:\s|$)/u;

module.exports = {
	directivesPattern,
};
