// import { Header } from '@/components/layout/Header'
import { CategoryGrid } from '@/components/home/<USER>'
// import { FeaturedProducts } from '@/components/home/<USER>'
import { HeroSection } from '@/components/home/<USER>'
// import { PWAInstaller } from '@/components/pwa/PWAInstaller'
// import { ThemeCustomizer } from '@/components/theme/ThemeProvider'

export default function Home() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow-sm border-b sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">B</span>
              </div>
              <span className="text-xl font-bold text-gray-900">Bayt</span>
            </div>
          </div>
        </div>
      </div>
      <main>
        <HeroSection />
        <CategoryGrid />
        <div className="py-16 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h2 className="text-3xl font-bold text-gray-900">Latest Listings</h2>
              <p className="mt-4 text-lg text-gray-600">
                Discover the newest items added to our marketplace
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
