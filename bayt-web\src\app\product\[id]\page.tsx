'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { Heart, MapPin, Eye, MessageCircle, Share2, Flag, Star } from 'lucide-react'
import { Header } from '@/components/layout/Header'
import { Button } from '@/components/ui/Button'
import { useAuth } from '@/contexts/AuthContext'
import { supabase, Product } from '@/lib/supabase'
import { formatPrice, formatRelativeTime, getImageUrl } from '@/lib/utils'
import { toast } from 'react-hot-toast'

export default function ProductPage() {
  const params = useParams()
  const { user } = useAuth()
  const [product, setProduct] = useState<Product | null>(null)
  const [loading, setLoading] = useState(true)
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [isFavorited, setIsFavorited] = useState(false)

  useEffect(() => {
    if (params.id) {
      fetchProduct(params.id as string)
    }
  }, [params.id])

  const fetchProduct = async (productId: string) => {
    try {
      const { data, error } = await supabase
        .from('products')
        .select(`
          *,
          category:categories(*),
          user:users(*)
        `)
        .eq('id', productId)
        .single()

      if (error) throw error
      setProduct(data)

      // Increment view count
      if (data) {
        await supabase.rpc('increment_product_views', {
          product_uuid: productId,
          user_uuid: user?.id || null,
        })
      }

      // Check if favorited
      if (user) {
        const { data: favorite } = await supabase
          .from('favorites')
          .select('id')
          .eq('user_id', user.id)
          .eq('product_id', productId)
          .single()

        setIsFavorited(!!favorite)
      }
    } catch (error) {
      console.error('Error fetching product:', error)
      toast.error('Product not found')
    } finally {
      setLoading(false)
    }
  }

  const handleFavorite = async () => {
    if (!user) {
      toast.error('Please sign in to add favorites')
      return
    }

    if (!product) return

    try {
      if (isFavorited) {
        await supabase
          .from('favorites')
          .delete()
          .eq('user_id', user.id)
          .eq('product_id', product.id)
      } else {
        await supabase
          .from('favorites')
          .insert({
            user_id: user.id,
            product_id: product.id,
          })
      }

      setIsFavorited(!isFavorited)
      toast.success(isFavorited ? 'Removed from favorites' : 'Added to favorites')
    } catch (error) {
      toast.error('Failed to update favorites')
    }
  }

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: product?.title,
          text: product?.description,
          url: window.location.href,
        })
      } catch (error) {
        // User cancelled sharing
      }
    } else {
      // Fallback to copying URL
      navigator.clipboard.writeText(window.location.href)
      toast.success('Link copied to clipboard')
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="bg-gray-200 h-96 rounded-lg"></div>
              <div className="space-y-4">
                <div className="bg-gray-200 h-8 rounded"></div>
                <div className="bg-gray-200 h-6 rounded w-3/4"></div>
                <div className="bg-gray-200 h-20 rounded"></div>
              </div>
            </div>
          </div>
        </main>
      </div>
    )
  }

  if (!product) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">Product not found</h1>
            <p className="text-gray-600 mt-2">The product you're looking for doesn't exist.</p>
          </div>
        </main>
      </div>
    )
  }

  const images = product.images && product.images.length > 0 
    ? product.images.map(getImageUrl)
    : ['/placeholder-image.jpg']

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Images */}
          <div>
            <div className="bg-white rounded-lg overflow-hidden shadow-sm">
              <img
                src={images[currentImageIndex]}
                alt={product.title}
                className="w-full h-96 object-cover"
              />
            </div>
            {images.length > 1 && (
              <div className="flex space-x-2 mt-4 overflow-x-auto">
                {images.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentImageIndex(index)}
                    className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 ${
                      index === currentImageIndex ? 'border-blue-500' : 'border-gray-200'
                    }`}
                  >
                    <img
                      src={image}
                      alt={`${product.title} ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Product Info */}
          <div className="space-y-6">
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <div className="flex items-start justify-between mb-4">
                <h1 className="text-2xl font-bold text-gray-900">{product.title}</h1>
                <div className="flex space-x-2">
                  <button
                    onClick={handleFavorite}
                    className="p-2 rounded-full hover:bg-gray-100"
                  >
                    <Heart
                      className={`w-6 h-6 ${
                        isFavorited ? 'text-red-500 fill-current' : 'text-gray-400'
                      }`}
                    />
                  </button>
                  <button
                    onClick={handleShare}
                    className="p-2 rounded-full hover:bg-gray-100"
                  >
                    <Share2 className="w-6 h-6 text-gray-400" />
                  </button>
                </div>
              </div>

              <div className="flex items-center justify-between mb-4">
                <span className="text-3xl font-bold text-blue-600">
                  {formatPrice(product.price, product.currency)}
                </span>
                <span className="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm capitalize">
                  {product.condition.replace('_', ' ')}
                </span>
              </div>

              <div className="flex items-center text-gray-600 mb-4">
                <MapPin className="w-5 h-5 mr-2" />
                <span>{product.location}</span>
              </div>

              <div className="flex items-center space-x-4 text-sm text-gray-500 mb-6">
                <div className="flex items-center">
                  <Eye className="w-4 h-4 mr-1" />
                  <span>{product.views_count} views</span>
                </div>
                <div className="flex items-center">
                  <Heart className="w-4 h-4 mr-1" />
                  <span>{product.favorites_count} favorites</span>
                </div>
                <span>{formatRelativeTime(product.created_at)}</span>
              </div>

              <div className="space-y-3">
                {user && user.id !== product.user_id ? (
                  <>
                    <Button className="w-full" size="lg">
                      <MessageCircle className="w-5 h-5 mr-2" />
                      Contact Seller
                    </Button>
                    <Button variant="outline" className="w-full">
                      Make Offer
                    </Button>
                  </>
                ) : user && user.id === product.user_id ? (
                  <div className="text-center py-4 bg-blue-50 rounded-lg">
                    <p className="text-blue-600 font-medium">This is your listing</p>
                  </div>
                ) : (
                  <Button className="w-full" size="lg">
                    Sign in to Contact Seller
                  </Button>
                )}
              </div>
            </div>

            {/* Seller Info */}
            {product.user && (
              <div className="bg-white rounded-lg p-6 shadow-sm">
                <h3 className="text-lg font-semibold mb-4">Seller Information</h3>
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center">
                    {product.user.avatar_url ? (
                      <img
                        src={product.user.avatar_url}
                        alt={product.user.full_name}
                        className="w-12 h-12 rounded-full object-cover"
                      />
                    ) : (
                      <span className="text-lg text-gray-600">
                        {product.user.full_name.charAt(0)}
                      </span>
                    )}
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">{product.user.full_name}</h4>
                    {product.user.rating && (
                      <div className="flex items-center mt-1">
                        <div className="flex items-center">
                          {Array.from({ length: 5 }).map((_, i) => (
                            <Star
                              key={i}
                              className={`w-4 h-4 ${
                                i < Math.floor(product.user!.rating!)
                                  ? 'text-yellow-400 fill-current'
                                  : 'text-gray-300'
                              }`}
                            />
                          ))}
                        </div>
                        <span className="text-sm text-gray-600 ml-2">
                          {product.user.rating.toFixed(1)} ({product.user.reviews_count} reviews)
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Report */}
            <div className="text-center">
              <button className="text-sm text-gray-500 hover:text-gray-700 flex items-center mx-auto">
                <Flag className="w-4 h-4 mr-1" />
                Report this listing
              </button>
            </div>
          </div>
        </div>

        {/* Description */}
        <div className="mt-8 bg-white rounded-lg p-6 shadow-sm">
          <h2 className="text-xl font-semibold mb-4">Description</h2>
          <div className="prose max-w-none">
            <p className="text-gray-700 whitespace-pre-wrap">{product.description}</p>
          </div>
        </div>

        {/* Category */}
        {product.category && (
          <div className="mt-8 bg-white rounded-lg p-6 shadow-sm">
            <h2 className="text-xl font-semibold mb-4">Category</h2>
            <div className="flex items-center">
              <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
                {product.category.name}
              </span>
            </div>
          </div>
        )}
      </main>
    </div>
  )
}
