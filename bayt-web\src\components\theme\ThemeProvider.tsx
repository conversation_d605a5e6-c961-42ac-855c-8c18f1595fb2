'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

type Theme = 'light' | 'dark' | 'auto'
type ColorScheme = 'blue' | 'purple' | 'green' | 'orange' | 'red' | 'pink'

interface ThemeContextType {
  theme: Theme
  colorScheme: ColorScheme
  setTheme: (theme: Theme) => void
  setColorScheme: (scheme: ColorScheme) => void
  isDark: boolean
  toggleTheme: () => void
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

const themes = {
  light: {
    background: 'bg-white',
    surface: 'bg-gray-50',
    text: 'text-gray-900',
    textSecondary: 'text-gray-600',
    border: 'border-gray-200',
    shadow: 'shadow-lg'
  },
  dark: {
    background: 'bg-gray-900',
    surface: 'bg-gray-800',
    text: 'text-white',
    textSecondary: 'text-gray-300',
    border: 'border-gray-700',
    shadow: 'shadow-2xl'
  }
}

const colorSchemes = {
  blue: {
    primary: 'bg-blue-600 hover:bg-blue-700',
    primaryText: 'text-blue-600',
    primaryBg: 'bg-blue-50',
    gradient: 'from-blue-600 to-blue-800'
  },
  purple: {
    primary: 'bg-purple-600 hover:bg-purple-700',
    primaryText: 'text-purple-600',
    primaryBg: 'bg-purple-50',
    gradient: 'from-purple-600 to-purple-800'
  },
  green: {
    primary: 'bg-green-600 hover:bg-green-700',
    primaryText: 'text-green-600',
    primaryBg: 'bg-green-50',
    gradient: 'from-green-600 to-green-800'
  },
  orange: {
    primary: 'bg-orange-600 hover:bg-orange-700',
    primaryText: 'text-orange-600',
    primaryBg: 'bg-orange-50',
    gradient: 'from-orange-600 to-orange-800'
  },
  red: {
    primary: 'bg-red-600 hover:bg-red-700',
    primaryText: 'text-red-600',
    primaryBg: 'bg-red-50',
    gradient: 'from-red-600 to-red-800'
  },
  pink: {
    primary: 'bg-pink-600 hover:bg-pink-700',
    primaryText: 'text-pink-600',
    primaryBg: 'bg-pink-50',
    gradient: 'from-pink-600 to-pink-800'
  }
}

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme, setTheme] = useState<Theme>('auto')
  const [colorScheme, setColorScheme] = useState<ColorScheme>('blue')
  const [isDark, setIsDark] = useState(false)

  useEffect(() => {
    // Load saved preferences
    const savedTheme = localStorage.getItem('theme') as Theme
    const savedColorScheme = localStorage.getItem('colorScheme') as ColorScheme
    
    if (savedTheme) setTheme(savedTheme)
    if (savedColorScheme) setColorScheme(savedColorScheme)
  }, [])

  useEffect(() => {
    const updateTheme = () => {
      let shouldBeDark = false

      if (theme === 'dark') {
        shouldBeDark = true
      } else if (theme === 'auto') {
        shouldBeDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      }

      setIsDark(shouldBeDark)
      
      // Update document class
      if (shouldBeDark) {
        document.documentElement.classList.add('dark')
      } else {
        document.documentElement.classList.remove('dark')
      }

      // Update meta theme-color
      const metaThemeColor = document.querySelector('meta[name="theme-color"]')
      if (metaThemeColor) {
        metaThemeColor.setAttribute('content', shouldBeDark ? '#1f2937' : '#ffffff')
      }
    }

    updateTheme()

    // Listen for system theme changes
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    const handleChange = () => {
      if (theme === 'auto') {
        updateTheme()
      }
    }

    mediaQuery.addEventListener('change', handleChange)
    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [theme])

  useEffect(() => {
    // Save preferences
    localStorage.setItem('theme', theme)
    localStorage.setItem('colorScheme', colorScheme)
  }, [theme, colorScheme])

  const toggleTheme = () => {
    setTheme(current => {
      if (current === 'light') return 'dark'
      if (current === 'dark') return 'auto'
      return 'light'
    })
  }

  const value = {
    theme,
    colorScheme,
    setTheme,
    setColorScheme,
    isDark,
    toggleTheme
  }

  return (
    <ThemeContext.Provider value={value}>
      <div className={`min-h-screen transition-colors duration-300 ${
        isDark ? themes.dark.background : themes.light.background
      }`}>
        {children}
      </div>
    </ThemeContext.Provider>
  )
}

export function useTheme() {
  const context = useContext(ThemeContext)
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}

// Theme customizer component
export function ThemeCustomizer() {
  const { theme, colorScheme, setTheme, setColorScheme, isDark } = useTheme()
  const [isOpen, setIsOpen] = useState(false)

  return (
    <>
      {/* Theme Toggle Button */}
      <motion.button
        onClick={() => setIsOpen(true)}
        className={`fixed bottom-20 right-6 w-12 h-12 rounded-full shadow-lg z-40 flex items-center justify-center transition-colors ${
          isDark ? 'bg-gray-800 text-white' : 'bg-white text-gray-900'
        }`}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
      >
        <motion.div
          animate={{ rotate: isDark ? 180 : 0 }}
          transition={{ duration: 0.3 }}
        >
          {isDark ? '🌙' : '☀️'}
        </motion.div>
      </motion.button>

      {/* Theme Customizer Panel */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 z-50"
              onClick={() => setIsOpen(false)}
            />

            {/* Panel */}
            <motion.div
              initial={{ x: '100%' }}
              animate={{ x: 0 }}
              exit={{ x: '100%' }}
              className={`fixed top-0 right-0 bottom-0 w-80 z-50 p-6 overflow-y-auto ${
                isDark ? 'bg-gray-900 text-white' : 'bg-white text-gray-900'
              }`}
            >
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold">Customize Theme</h2>
                <motion.button
                  onClick={() => setIsOpen(false)}
                  className={`p-2 rounded-lg transition-colors ${
                    isDark ? 'hover:bg-gray-800' : 'hover:bg-gray-100'
                  }`}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  ✕
                </motion.button>
              </div>

              {/* Theme Selection */}
              <div className="mb-8">
                <h3 className="text-lg font-semibold mb-4">Theme Mode</h3>
                <div className="grid grid-cols-3 gap-2">
                  {(['light', 'dark', 'auto'] as Theme[]).map((themeOption) => (
                    <motion.button
                      key={themeOption}
                      onClick={() => setTheme(themeOption)}
                      className={`p-3 rounded-lg border-2 transition-all ${
                        theme === themeOption
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                          : isDark
                          ? 'border-gray-700 hover:border-gray-600'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <div className="text-2xl mb-1">
                        {themeOption === 'light' && '☀️'}
                        {themeOption === 'dark' && '🌙'}
                        {themeOption === 'auto' && '🔄'}
                      </div>
                      <div className="text-sm font-medium capitalize">
                        {themeOption}
                      </div>
                    </motion.button>
                  ))}
                </div>
              </div>

              {/* Color Scheme Selection */}
              <div className="mb-8">
                <h3 className="text-lg font-semibold mb-4">Color Scheme</h3>
                <div className="grid grid-cols-3 gap-2">
                  {(Object.keys(colorSchemes) as ColorScheme[]).map((scheme) => (
                    <motion.button
                      key={scheme}
                      onClick={() => setColorScheme(scheme)}
                      className={`p-3 rounded-lg border-2 transition-all ${
                        colorScheme === scheme
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                          : isDark
                          ? 'border-gray-700 hover:border-gray-600'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <div className={`w-8 h-8 rounded-full mx-auto mb-2 ${
                        scheme === 'blue' ? 'bg-blue-500' :
                        scheme === 'purple' ? 'bg-purple-500' :
                        scheme === 'green' ? 'bg-green-500' :
                        scheme === 'orange' ? 'bg-orange-500' :
                        scheme === 'red' ? 'bg-red-500' :
                        'bg-pink-500'
                      }`} />
                      <div className="text-sm font-medium capitalize">
                        {scheme}
                      </div>
                    </motion.button>
                  ))}
                </div>
              </div>

              {/* Preview */}
              <div className="mb-8">
                <h3 className="text-lg font-semibold mb-4">Preview</h3>
                <div className={`p-4 rounded-lg border ${
                  isDark ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'
                }`}>
                  <div className="flex items-center gap-3 mb-3">
                    <div className={`w-10 h-10 rounded-full ${colorSchemes[colorScheme].primary}`} />
                    <div>
                      <div className="font-medium">Sample Product</div>
                      <div className={isDark ? 'text-gray-400' : 'text-gray-600'}>
                        $299.99
                      </div>
                    </div>
                  </div>
                  <button className={`w-full py-2 px-4 rounded-lg text-white font-medium ${colorSchemes[colorScheme].primary}`}>
                    Add to Cart
                  </button>
                </div>
              </div>

              {/* Reset Button */}
              <motion.button
                onClick={() => {
                  setTheme('auto')
                  setColorScheme('blue')
                }}
                className={`w-full py-3 px-4 rounded-lg border transition-colors ${
                  isDark
                    ? 'border-gray-700 hover:bg-gray-800'
                    : 'border-gray-200 hover:bg-gray-50'
                }`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                Reset to Default
              </motion.button>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  )
}

// Hook for theme-aware styling
export function useThemeClasses() {
  const { isDark, colorScheme } = useTheme()
  
  const current = isDark ? themes.dark : themes.light
  const colors = colorSchemes[colorScheme]
  
  return {
    ...current,
    ...colors,
    isDark
  }
}

// Animated theme transition component
export function ThemeTransition({ children }: { children: React.ReactNode }) {
  const { isDark } = useTheme()
  
  return (
    <motion.div
      key={isDark ? 'dark' : 'light'}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3 }}
    >
      {children}
    </motion.div>
  )
}
