{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Bayt/bayt-web/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://demo.supabase.co'\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'demo_key'\n\n// Create real Supabase client or mock for demo\nconst isDemo = !process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL.includes('demo')\n\nexport const supabase = isDemo ? createMockSupabase() : createClient(supabaseUrl, supabaseAnonKey)\n\n// Mock data for demo\nconst mockCategories = [\n  { id: '1', name: 'Electronics', name_ar: 'إلكترونيات', name_tr: 'Elektronik', icon: 'smartphone', parent_id: null, sort_order: 1, is_active: true, created_at: new Date().toISOString() },\n  { id: '2', name: 'Vehicles', name_ar: 'مركبات', name_tr: 'Araçlar', icon: 'car', parent_id: null, sort_order: 2, is_active: true, created_at: new Date().toISOString() },\n  { id: '3', name: 'Real Estate', name_ar: 'عقارات', name_tr: 'Emlak', icon: 'home', parent_id: null, sort_order: 3, is_active: true, created_at: new Date().toISOString() },\n  { id: '4', name: 'Fashion & Beauty', name_ar: 'أزياء وجمال', name_tr: 'Moda ve Güzellik', icon: 'shirt', parent_id: null, sort_order: 4, is_active: true, created_at: new Date().toISOString() },\n  { id: '5', name: 'Home & Garden', name_ar: 'منزل وحديقة', name_tr: 'Ev ve Bahçe', icon: 'sofa', parent_id: null, sort_order: 5, is_active: true, created_at: new Date().toISOString() },\n  { id: '6', name: 'Sports & Hobbies', name_ar: 'رياضة وهوايات', name_tr: 'Spor ve Hobi', icon: 'dumbbell', parent_id: null, sort_order: 6, is_active: true, created_at: new Date().toISOString() },\n]\n\nconst mockProducts = [\n  {\n    id: '1',\n    title: 'iPhone 15 Pro Max',\n    title_ar: 'آيفون 15 برو ماكس',\n    title_tr: 'iPhone 15 Pro Max',\n    description: 'Brand new iPhone 15 Pro Max with 256GB storage. Excellent condition with original box.',\n    description_ar: 'آيفون 15 برو ماكس جديد مع 256 جيجابايت تخزين. حالة ممتازة مع الصندوق الأصلي.',\n    description_tr: 'Yepyeni iPhone 15 Pro Max 256GB depolama. Orijinal kutusu ile mükemmel durumda.',\n    price: 1200,\n    currency: 'USD',\n    condition: 'new',\n    category_id: '1',\n    user_id: 'demo-user-1',\n    images: ['https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400'],\n    location: 'Damascus, Syria',\n    latitude: 33.5138,\n    longitude: 36.2765,\n    is_active: true,\n    is_sold: false,\n    is_featured: true,\n    views_count: 245,\n    favorites_count: 18,\n    created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),\n    updated_at: new Date().toISOString(),\n    category: mockCategories[0],\n    user: { id: 'demo-user-1', full_name: 'Ahmed Hassan', avatar_url: null, rating: 4.8, reviews_count: 12 }\n  },\n  {\n    id: '2',\n    title: 'BMW X5 2020',\n    title_ar: 'بي إم دبليو إكس 5 2020',\n    title_tr: 'BMW X5 2020',\n    description: 'Excellent condition BMW X5 with low mileage. Full service history available.',\n    description_ar: 'بي إم دبليو إكس 5 في حالة ممتازة مع عدد كيلومترات قليل. تاريخ خدمة كامل متاح.',\n    description_tr: 'Düşük kilometre ile mükemmel durumda BMW X5. Tam servis geçmişi mevcut.',\n    price: 45000,\n    currency: 'USD',\n    condition: 'like_new',\n    category_id: '2',\n    user_id: 'demo-user-2',\n    images: ['https://images.unsplash.com/photo-1555215695-3004980ad54e?w=400'],\n    location: 'Aleppo, Syria',\n    latitude: 36.2021,\n    longitude: 37.1343,\n    is_active: true,\n    is_sold: false,\n    is_featured: false,\n    views_count: 89,\n    favorites_count: 7,\n    created_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),\n    updated_at: new Date().toISOString(),\n    category: mockCategories[1],\n    user: { id: 'demo-user-2', full_name: 'Sara Ali', avatar_url: null, rating: 4.5, reviews_count: 8 }\n  },\n  {\n    id: '3',\n    title: 'Modern Apartment',\n    title_ar: 'شقة حديثة',\n    title_tr: 'Modern Daire',\n    description: 'Beautiful 3-bedroom apartment in the heart of Damascus. Fully furnished with modern amenities.',\n    description_ar: 'شقة جميلة من 3 غرف نوم في قلب دمشق. مفروشة بالكامل مع وسائل الراحة الحديثة.',\n    description_tr: 'Şam\\'ın kalbinde güzel 3 yatak odalı daire. Modern olanaklarla tamamen döşeli.',\n    price: 150000,\n    currency: 'USD',\n    condition: 'good',\n    category_id: '3',\n    user_id: 'demo-user-3',\n    images: ['https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=400'],\n    location: 'Damascus, Syria',\n    latitude: 33.5138,\n    longitude: 36.2765,\n    is_active: true,\n    is_sold: false,\n    is_featured: true,\n    views_count: 156,\n    favorites_count: 23,\n    created_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),\n    updated_at: new Date().toISOString(),\n    category: mockCategories[2],\n    user: { id: 'demo-user-3', full_name: 'Omar Khalil', avatar_url: null, rating: 4.9, reviews_count: 15 }\n  }\n]\n\nfunction createMockSupabase() {\n  return {\n    auth: {\n      getSession: () => Promise.resolve({ data: { session: null }, error: null }),\n      onAuthStateChange: (callback: any) => {\n        // Simulate auth state change\n        setTimeout(() => callback('SIGNED_OUT', null), 100)\n        return { data: { subscription: { unsubscribe: () => {} } } }\n      },\n      signUp: (credentials: any) => {\n        console.log('Mock signUp:', credentials)\n        return Promise.resolve({\n          data: { user: { id: 'demo-user', email: credentials.email }, session: null },\n          error: null\n        })\n      },\n      signInWithPassword: (credentials: any) => {\n        console.log('Mock signIn:', credentials)\n        return Promise.resolve({\n          data: { user: { id: 'demo-user', email: credentials.email }, session: { user: { id: 'demo-user' } } },\n          error: null\n        })\n      },\n      signOut: () => Promise.resolve({ error: null }),\n      resetPasswordForEmail: () => Promise.resolve({ error: null }),\n    },\n    from: (table: string) => ({\n      select: (columns?: string) => ({\n        eq: (column: string, value: any) => ({\n          single: () => {\n            if (table === 'categories') {\n              const category = mockCategories.find(c => c.id === value)\n              return Promise.resolve({ data: category || null, error: null })\n            }\n            if (table === 'products') {\n              const product = mockProducts.find(p => p.id === value)\n              return Promise.resolve({ data: product || null, error: null })\n            }\n            return Promise.resolve({ data: null, error: null })\n          },\n          order: (column: string, options?: any) => ({\n            limit: (count: number) => {\n              if (table === 'products') {\n                return Promise.resolve({ data: mockProducts.slice(0, count), error: null })\n              }\n              return Promise.resolve({ data: [], error: null })\n            }\n          })\n        }),\n        is: (column: string, value: any) => ({\n          eq: (column2: string, value2: any) => ({\n            order: (column3: string, options?: any) => {\n              if (table === 'categories' && column === 'parent_id' && value === null) {\n                return Promise.resolve({ data: mockCategories, error: null })\n              }\n              return Promise.resolve({ data: [], error: null })\n            }\n          })\n        }),\n        order: (column: string, options?: any) => ({\n          limit: (count: number) => {\n            if (table === 'categories') {\n              return Promise.resolve({ data: mockCategories.slice(0, count), error: null })\n            }\n            if (table === 'products') {\n              return Promise.resolve({ data: mockProducts.slice(0, count), error: null })\n            }\n            return Promise.resolve({ data: [], error: null })\n          }\n        })\n      }),\n      insert: (data: any) => ({\n        select: (columns?: string) => ({\n          single: () => {\n            console.log('Mock insert:', table, data)\n            return Promise.resolve({ data: { id: 'new-id', ...data }, error: null })\n          }\n        })\n      }),\n      update: (data: any) => ({\n        eq: (column: string, value: any) => {\n          console.log('Mock update:', table, data)\n          return Promise.resolve({ error: null })\n        }\n      }),\n      delete: () => ({\n        eq: (column: string, value: any) => {\n          console.log('Mock delete:', table, value)\n          return Promise.resolve({ error: null })\n        }\n      })\n    }),\n    rpc: (functionName: string, params?: any) => {\n      console.log('Mock RPC:', functionName, params)\n      return Promise.resolve({ data: null, error: null })\n    },\n    storage: {\n      from: (bucket: string) => ({\n        upload: (path: string, file: any) => {\n          console.log('Mock upload:', bucket, path)\n          return Promise.resolve({ error: null })\n        }\n      })\n    }\n  }\n}\n\n// Database Types\nexport interface User {\n  id: string\n  email: string\n  phone?: string\n  full_name: string\n  avatar_url?: string\n  location?: string\n  rating?: number\n  created_at: string\n  updated_at: string\n}\n\nexport interface Category {\n  id: string\n  name: string\n  name_ar: string\n  name_tr: string\n  icon: string\n  parent_id?: string\n  created_at: string\n}\n\nexport interface Product {\n  id: string\n  title: string\n  title_ar: string\n  title_tr: string\n  description: string\n  description_ar: string\n  description_tr: string\n  price: number\n  currency: string\n  condition: 'new' | 'like_new' | 'good' | 'fair' | 'poor'\n  category_id: string\n  user_id: string\n  images: string[]\n  location: string\n  latitude?: number\n  longitude?: number\n  is_active: boolean\n  is_sold: boolean\n  views_count: number\n  favorites_count: number\n  created_at: string\n  updated_at: string\n  category?: Category\n  user?: User\n}\n\nexport interface Message {\n  id: string\n  product_id: string\n  sender_id: string\n  receiver_id: string\n  content: string\n  is_read: boolean\n  created_at: string\n  product?: Product\n  sender?: User\n  receiver?: User\n}\n\nexport interface Favorite {\n  id: string\n  user_id: string\n  product_id: string\n  created_at: string\n  product?: Product\n}\n\nexport interface Review {\n  id: string\n  reviewer_id: string\n  reviewed_id: string\n  product_id?: string\n  rating: number\n  comment?: string\n  created_at: string\n  reviewer?: User\n  reviewed?: User\n  product?: Product\n}\n"], "names": [], "mappings": ";;;AAEoB;AAFpB;;AAEA,MAAM,cAAc,gEAAwC;AAC5D,MAAM,kBAAkB,qDAA6C;AAErE,+CAA+C;AAC/C,MAAM,SAAS,iEAAyC,6DAAqC,QAAQ,CAAC;AAE/F,MAAM,WAAW,SAAS,uBAAuB,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAElF,qBAAqB;AACrB,MAAM,iBAAiB;IACrB;QAAE,IAAI;QAAK,MAAM;QAAe,SAAS;QAAc,SAAS;QAAc,MAAM;QAAc,WAAW;QAAM,YAAY;QAAG,WAAW;QAAM,YAAY,IAAI,OAAO,WAAW;IAAG;IACxL;QAAE,IAAI;QAAK,MAAM;QAAY,SAAS;QAAU,SAAS;QAAW,MAAM;QAAO,WAAW;QAAM,YAAY;QAAG,WAAW;QAAM,YAAY,IAAI,OAAO,WAAW;IAAG;IACvK;QAAE,IAAI;QAAK,MAAM;QAAe,SAAS;QAAU,SAAS;QAAS,MAAM;QAAQ,WAAW;QAAM,YAAY;QAAG,WAAW;QAAM,YAAY,IAAI,OAAO,WAAW;IAAG;IACzK;QAAE,IAAI;QAAK,MAAM;QAAoB,SAAS;QAAe,SAAS;QAAoB,MAAM;QAAS,WAAW;QAAM,YAAY;QAAG,WAAW;QAAM,YAAY,IAAI,OAAO,WAAW;IAAG;IAC/L;QAAE,IAAI;QAAK,MAAM;QAAiB,SAAS;QAAe,SAAS;QAAe,MAAM;QAAQ,WAAW;QAAM,YAAY;QAAG,WAAW;QAAM,YAAY,IAAI,OAAO,WAAW;IAAG;IACtL;QAAE,IAAI;QAAK,MAAM;QAAoB,SAAS;QAAiB,SAAS;QAAgB,MAAM;QAAY,WAAW;QAAM,YAAY;QAAG,WAAW;QAAM,YAAY,IAAI,OAAO,WAAW;IAAG;CACjM;AAED,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,UAAU;QACV,aAAa;QACb,gBAAgB;QAChB,gBAAgB;QAChB,OAAO;QACP,UAAU;QACV,WAAW;QACX,aAAa;QACb,SAAS;QACT,QAAQ;YAAC;SAAqE;QAC9E,UAAU;QACV,UAAU;QACV,WAAW;QACX,WAAW;QACX,SAAS;QACT,aAAa;QACb,aAAa;QACb,iBAAiB;QACjB,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;QACtE,YAAY,IAAI,OAAO,WAAW;QAClC,UAAU,cAAc,CAAC,EAAE;QAC3B,MAAM;YAAE,IAAI;YAAe,WAAW;YAAgB,YAAY;YAAM,QAAQ;YAAK,eAAe;QAAG;IACzG;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,UAAU;QACV,aAAa;QACb,gBAAgB;QAChB,gBAAgB;QAChB,OAAO;QACP,UAAU;QACV,WAAW;QACX,aAAa;QACb,SAAS;QACT,QAAQ;YAAC;SAAkE;QAC3E,UAAU;QACV,UAAU;QACV,WAAW;QACX,WAAW;QACX,SAAS;QACT,aAAa;QACb,aAAa;QACb,iBAAiB;QACjB,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;QACtE,YAAY,IAAI,OAAO,WAAW;QAClC,UAAU,cAAc,CAAC,EAAE;QAC3B,MAAM;YAAE,IAAI;YAAe,WAAW;YAAY,YAAY;YAAM,QAAQ;YAAK,eAAe;QAAE;IACpG;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,UAAU;QACV,aAAa;QACb,gBAAgB;QAChB,gBAAgB;QAChB,OAAO;QACP,UAAU;QACV,WAAW;QACX,aAAa;QACb,SAAS;QACT,QAAQ;YAAC;SAAkE;QAC3E,UAAU;QACV,UAAU;QACV,WAAW;QACX,WAAW;QACX,SAAS;QACT,aAAa;QACb,aAAa;QACb,iBAAiB;QACjB,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;QACtE,YAAY,IAAI,OAAO,WAAW;QAClC,UAAU,cAAc,CAAC,EAAE;QAC3B,MAAM;YAAE,IAAI;YAAe,WAAW;YAAe,YAAY;YAAM,QAAQ;YAAK,eAAe;QAAG;IACxG;CACD;AAED,SAAS;IACP,OAAO;QACL,MAAM;YACJ,YAAY,IAAM,QAAQ,OAAO,CAAC;oBAAE,MAAM;wBAAE,SAAS;oBAAK;oBAAG,OAAO;gBAAK;YACzE,mBAAmB,CAAC;gBAClB,6BAA6B;gBAC7B,WAAW,IAAM,SAAS,cAAc,OAAO;gBAC/C,OAAO;oBAAE,MAAM;wBAAE,cAAc;4BAAE,aAAa,KAAO;wBAAE;oBAAE;gBAAE;YAC7D;YACA,QAAQ,CAAC;gBACP,QAAQ,GAAG,CAAC,gBAAgB;gBAC5B,OAAO,QAAQ,OAAO,CAAC;oBACrB,MAAM;wBAAE,MAAM;4BAAE,IAAI;4BAAa,OAAO,YAAY,KAAK;wBAAC;wBAAG,SAAS;oBAAK;oBAC3E,OAAO;gBACT;YACF;YACA,oBAAoB,CAAC;gBACnB,QAAQ,GAAG,CAAC,gBAAgB;gBAC5B,OAAO,QAAQ,OAAO,CAAC;oBACrB,MAAM;wBAAE,MAAM;4BAAE,IAAI;4BAAa,OAAO,YAAY,KAAK;wBAAC;wBAAG,SAAS;4BAAE,MAAM;gCAAE,IAAI;4BAAY;wBAAE;oBAAE;oBACpG,OAAO;gBACT;YACF;YACA,SAAS,IAAM,QAAQ,OAAO,CAAC;oBAAE,OAAO;gBAAK;YAC7C,uBAAuB,IAAM,QAAQ,OAAO,CAAC;oBAAE,OAAO;gBAAK;QAC7D;QACA,MAAM,CAAC,QAAkB,CAAC;gBACxB,QAAQ,CAAC,UAAqB,CAAC;wBAC7B,IAAI,CAAC,QAAgB,QAAe,CAAC;gCACnC,QAAQ;oCACN,IAAI,UAAU,cAAc;wCAC1B,MAAM,WAAW,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;wCACnD,OAAO,QAAQ,OAAO,CAAC;4CAAE,MAAM,YAAY;4CAAM,OAAO;wCAAK;oCAC/D;oCACA,IAAI,UAAU,YAAY;wCACxB,MAAM,UAAU,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;wCAChD,OAAO,QAAQ,OAAO,CAAC;4CAAE,MAAM,WAAW;4CAAM,OAAO;wCAAK;oCAC9D;oCACA,OAAO,QAAQ,OAAO,CAAC;wCAAE,MAAM;wCAAM,OAAO;oCAAK;gCACnD;gCACA,OAAO,CAAC,QAAgB,UAAkB,CAAC;wCACzC,OAAO,CAAC;4CACN,IAAI,UAAU,YAAY;gDACxB,OAAO,QAAQ,OAAO,CAAC;oDAAE,MAAM,aAAa,KAAK,CAAC,GAAG;oDAAQ,OAAO;gDAAK;4CAC3E;4CACA,OAAO,QAAQ,OAAO,CAAC;gDAAE,MAAM,EAAE;gDAAE,OAAO;4CAAK;wCACjD;oCACF,CAAC;4BACH,CAAC;wBACD,IAAI,CAAC,QAAgB,QAAe,CAAC;gCACnC,IAAI,CAAC,SAAiB,SAAgB,CAAC;wCACrC,OAAO,CAAC,SAAiB;4CACvB,IAAI,UAAU,gBAAgB,WAAW,eAAe,UAAU,MAAM;gDACtE,OAAO,QAAQ,OAAO,CAAC;oDAAE,MAAM;oDAAgB,OAAO;gDAAK;4CAC7D;4CACA,OAAO,QAAQ,OAAO,CAAC;gDAAE,MAAM,EAAE;gDAAE,OAAO;4CAAK;wCACjD;oCACF,CAAC;4BACH,CAAC;wBACD,OAAO,CAAC,QAAgB,UAAkB,CAAC;gCACzC,OAAO,CAAC;oCACN,IAAI,UAAU,cAAc;wCAC1B,OAAO,QAAQ,OAAO,CAAC;4CAAE,MAAM,eAAe,KAAK,CAAC,GAAG;4CAAQ,OAAO;wCAAK;oCAC7E;oCACA,IAAI,UAAU,YAAY;wCACxB,OAAO,QAAQ,OAAO,CAAC;4CAAE,MAAM,aAAa,KAAK,CAAC,GAAG;4CAAQ,OAAO;wCAAK;oCAC3E;oCACA,OAAO,QAAQ,OAAO,CAAC;wCAAE,MAAM,EAAE;wCAAE,OAAO;oCAAK;gCACjD;4BACF,CAAC;oBACH,CAAC;gBACD,QAAQ,CAAC,OAAc,CAAC;wBACtB,QAAQ,CAAC,UAAqB,CAAC;gCAC7B,QAAQ;oCACN,QAAQ,GAAG,CAAC,gBAAgB,OAAO;oCACnC,OAAO,QAAQ,OAAO,CAAC;wCAAE,MAAM;4CAAE,IAAI;4CAAU,GAAG,IAAI;wCAAC;wCAAG,OAAO;oCAAK;gCACxE;4BACF,CAAC;oBACH,CAAC;gBACD,QAAQ,CAAC,OAAc,CAAC;wBACtB,IAAI,CAAC,QAAgB;4BACnB,QAAQ,GAAG,CAAC,gBAAgB,OAAO;4BACnC,OAAO,QAAQ,OAAO,CAAC;gCAAE,OAAO;4BAAK;wBACvC;oBACF,CAAC;gBACD,QAAQ,IAAM,CAAC;wBACb,IAAI,CAAC,QAAgB;4BACnB,QAAQ,GAAG,CAAC,gBAAgB,OAAO;4BACnC,OAAO,QAAQ,OAAO,CAAC;gCAAE,OAAO;4BAAK;wBACvC;oBACF,CAAC;YACH,CAAC;QACD,KAAK,CAAC,cAAsB;YAC1B,QAAQ,GAAG,CAAC,aAAa,cAAc;YACvC,OAAO,QAAQ,OAAO,CAAC;gBAAE,MAAM;gBAAM,OAAO;YAAK;QACnD;QACA,SAAS;YACP,MAAM,CAAC,SAAmB,CAAC;oBACzB,QAAQ,CAAC,MAAc;wBACrB,QAAQ,GAAG,CAAC,gBAAgB,QAAQ;wBACpC,OAAO,QAAQ,OAAO,CAAC;4BAAE,OAAO;wBAAK;oBACvC;gBACF,CAAC;QACH;IACF;AACF", "debugId": null}}, {"offset": {"line": 385, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Bayt/bayt-web/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState } from 'react'\nimport { User as SupabaseUser, Session } from '@supabase/supabase-js'\nimport { supabase, User } from '@/lib/supabase'\nimport { toast } from 'react-hot-toast'\n\ninterface AuthContextType {\n  user: User | null\n  session: Session | null\n  loading: boolean\n  signUp: (email: string, password: string, fullName: string) => Promise<void>\n  signIn: (email: string, password: string) => Promise<void>\n  signOut: () => Promise<void>\n  updateProfile: (updates: Partial<User>) => Promise<void>\n  resetPassword: (email: string) => Promise<void>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [session, setSession] = useState<Session | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    // Get initial session\n    supabase.auth.getSession().then(({ data: { session } }) => {\n      setSession(session)\n      if (session?.user) {\n        fetchUserProfile(session.user.id)\n      } else {\n        setLoading(false)\n      }\n    })\n\n    // Listen for auth changes\n    const {\n      data: { subscription },\n    } = supabase.auth.onAuthStateChange(async (event, session) => {\n      setSession(session)\n      \n      if (session?.user) {\n        await fetchUserProfile(session.user.id)\n      } else {\n        setUser(null)\n        setLoading(false)\n      }\n    })\n\n    return () => subscription.unsubscribe()\n  }, [])\n\n  const fetchUserProfile = async (userId: string) => {\n    try {\n      const { data, error } = await supabase\n        .from('users')\n        .select('*')\n        .eq('id', userId)\n        .single()\n\n      if (error) throw error\n      setUser(data)\n    } catch (error) {\n      console.error('Error fetching user profile:', error)\n      toast.error('Failed to load user profile')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const signUp = async (email: string, password: string, fullName: string) => {\n    try {\n      setLoading(true)\n      const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n          data: {\n            full_name: fullName,\n          },\n        },\n      })\n\n      if (error) throw error\n\n      if (data.user && !data.session) {\n        toast.success('Please check your email to confirm your account')\n      } else {\n        toast.success('Account created successfully!')\n      }\n    } catch (error: any) {\n      toast.error(error.message || 'Failed to create account')\n      throw error\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const signIn = async (email: string, password: string) => {\n    try {\n      setLoading(true)\n      const { error } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      })\n\n      if (error) throw error\n      toast.success('Signed in successfully!')\n    } catch (error: any) {\n      toast.error(error.message || 'Failed to sign in')\n      throw error\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const signOut = async () => {\n    try {\n      setLoading(true)\n      const { error } = await supabase.auth.signOut()\n      if (error) throw error\n      toast.success('Signed out successfully!')\n    } catch (error: any) {\n      toast.error(error.message || 'Failed to sign out')\n      throw error\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const updateProfile = async (updates: Partial<User>) => {\n    if (!user) throw new Error('No user logged in')\n\n    try {\n      setLoading(true)\n      const { error } = await supabase\n        .from('users')\n        .update(updates)\n        .eq('id', user.id)\n\n      if (error) throw error\n\n      setUser({ ...user, ...updates })\n      toast.success('Profile updated successfully!')\n    } catch (error: any) {\n      toast.error(error.message || 'Failed to update profile')\n      throw error\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const resetPassword = async (email: string) => {\n    try {\n      const { error } = await supabase.auth.resetPasswordForEmail(email, {\n        redirectTo: `${window.location.origin}/reset-password`,\n      })\n\n      if (error) throw error\n      toast.success('Password reset email sent!')\n    } catch (error: any) {\n      toast.error(error.message || 'Failed to send reset email')\n      throw error\n    }\n  }\n\n  const value = {\n    user,\n    session,\n    loading,\n    signUp,\n    signIn,\n    signOut,\n    updateProfile,\n    resetPassword,\n  }\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AACA;;;AALA;;;;AAkBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,sBAAsB;YACtB,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI;0CAAC,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE;oBACpD,WAAW;oBACX,IAAI,SAAS,MAAM;wBACjB,iBAAiB,QAAQ,IAAI,CAAC,EAAE;oBAClC,OAAO;wBACL,WAAW;oBACb;gBACF;;YAEA,0BAA0B;YAC1B,MAAM,EACJ,MAAM,EAAE,YAAY,EAAE,EACvB,GAAG,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB;0CAAC,OAAO,OAAO;oBAChD,WAAW;oBAEX,IAAI,SAAS,MAAM;wBACjB,MAAM,iBAAiB,QAAQ,IAAI,CAAC,EAAE;oBACxC,OAAO;wBACL,QAAQ;wBACR,WAAW;oBACb;gBACF;;YAEA;0CAAO,IAAM,aAAa,WAAW;;QACvC;iCAAG,EAAE;IAEL,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;YAET,IAAI,OAAO,MAAM;YACjB,QAAQ;QACV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,SAAS,OAAO,OAAe,UAAkB;QACrD,IAAI;YACF,WAAW;YACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;gBACjD;gBACA;gBACA,SAAS;oBACP,MAAM;wBACJ,WAAW;oBACb;gBACF;YACF;YAEA,IAAI,OAAO,MAAM;YAEjB,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,OAAO,EAAE;gBAC9B,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;QACF,EAAE,OAAO,OAAY;YACnB,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC7B,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,IAAI;YACF,WAAW;YACX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;gBACvD;gBACA;YACF;YAEA,IAAI,OAAO,MAAM;YACjB,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAY;YACnB,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC7B,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,UAAU;QACd,IAAI;YACF,WAAW;YACX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;YAC7C,IAAI,OAAO,MAAM;YACjB,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAY;YACnB,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC7B,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;QAE3B,IAAI;YACF,WAAW;YACX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,SACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,KAAK,EAAE;YAEnB,IAAI,OAAO,MAAM;YAEjB,QAAQ;gBAAE,GAAG,IAAI;gBAAE,GAAG,OAAO;YAAC;YAC9B,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAY;YACnB,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC7B,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO;gBACjE,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,eAAe,CAAC;YACxD;YAEA,IAAI,OAAO,MAAM;YACjB,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAY;YACnB,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC7B,MAAM;QACR;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBAAO,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;GA/JgB;KAAA;AAiKT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 572, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Bayt/bayt-web/src/components/theme/ThemeProvider.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\n\ntype Theme = 'light' | 'dark' | 'auto'\ntype ColorScheme = 'blue' | 'purple' | 'green' | 'orange' | 'red' | 'pink'\n\ninterface ThemeContextType {\n  theme: Theme\n  colorScheme: ColorScheme\n  setTheme: (theme: Theme) => void\n  setColorScheme: (scheme: ColorScheme) => void\n  isDark: boolean\n  toggleTheme: () => void\n}\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined)\n\nconst themes = {\n  light: {\n    background: 'bg-white',\n    surface: 'bg-gray-50',\n    text: 'text-gray-900',\n    textSecondary: 'text-gray-600',\n    border: 'border-gray-200',\n    shadow: 'shadow-lg'\n  },\n  dark: {\n    background: 'bg-gray-900',\n    surface: 'bg-gray-800',\n    text: 'text-white',\n    textSecondary: 'text-gray-300',\n    border: 'border-gray-700',\n    shadow: 'shadow-2xl'\n  }\n}\n\nconst colorSchemes = {\n  blue: {\n    primary: 'bg-blue-600 hover:bg-blue-700',\n    primaryText: 'text-blue-600',\n    primaryBg: 'bg-blue-50',\n    gradient: 'from-blue-600 to-blue-800'\n  },\n  purple: {\n    primary: 'bg-purple-600 hover:bg-purple-700',\n    primaryText: 'text-purple-600',\n    primaryBg: 'bg-purple-50',\n    gradient: 'from-purple-600 to-purple-800'\n  },\n  green: {\n    primary: 'bg-green-600 hover:bg-green-700',\n    primaryText: 'text-green-600',\n    primaryBg: 'bg-green-50',\n    gradient: 'from-green-600 to-green-800'\n  },\n  orange: {\n    primary: 'bg-orange-600 hover:bg-orange-700',\n    primaryText: 'text-orange-600',\n    primaryBg: 'bg-orange-50',\n    gradient: 'from-orange-600 to-orange-800'\n  },\n  red: {\n    primary: 'bg-red-600 hover:bg-red-700',\n    primaryText: 'text-red-600',\n    primaryBg: 'bg-red-50',\n    gradient: 'from-red-600 to-red-800'\n  },\n  pink: {\n    primary: 'bg-pink-600 hover:bg-pink-700',\n    primaryText: 'text-pink-600',\n    primaryBg: 'bg-pink-50',\n    gradient: 'from-pink-600 to-pink-800'\n  }\n}\n\nexport function ThemeProvider({ children }: { children: React.ReactNode }) {\n  const [theme, setTheme] = useState<Theme>('auto')\n  const [colorScheme, setColorScheme] = useState<ColorScheme>('blue')\n  const [isDark, setIsDark] = useState(false)\n\n  useEffect(() => {\n    // Load saved preferences\n    const savedTheme = localStorage.getItem('theme') as Theme\n    const savedColorScheme = localStorage.getItem('colorScheme') as ColorScheme\n    \n    if (savedTheme) setTheme(savedTheme)\n    if (savedColorScheme) setColorScheme(savedColorScheme)\n  }, [])\n\n  useEffect(() => {\n    const updateTheme = () => {\n      let shouldBeDark = false\n\n      if (theme === 'dark') {\n        shouldBeDark = true\n      } else if (theme === 'auto') {\n        shouldBeDark = window.matchMedia('(prefers-color-scheme: dark)').matches\n      }\n\n      setIsDark(shouldBeDark)\n      \n      // Update document class\n      if (shouldBeDark) {\n        document.documentElement.classList.add('dark')\n      } else {\n        document.documentElement.classList.remove('dark')\n      }\n\n      // Update meta theme-color\n      const metaThemeColor = document.querySelector('meta[name=\"theme-color\"]')\n      if (metaThemeColor) {\n        metaThemeColor.setAttribute('content', shouldBeDark ? '#1f2937' : '#ffffff')\n      }\n    }\n\n    updateTheme()\n\n    // Listen for system theme changes\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')\n    const handleChange = () => {\n      if (theme === 'auto') {\n        updateTheme()\n      }\n    }\n\n    mediaQuery.addEventListener('change', handleChange)\n    return () => mediaQuery.removeEventListener('change', handleChange)\n  }, [theme])\n\n  useEffect(() => {\n    // Save preferences\n    localStorage.setItem('theme', theme)\n    localStorage.setItem('colorScheme', colorScheme)\n  }, [theme, colorScheme])\n\n  const toggleTheme = () => {\n    setTheme(current => {\n      if (current === 'light') return 'dark'\n      if (current === 'dark') return 'auto'\n      return 'light'\n    })\n  }\n\n  const value = {\n    theme,\n    colorScheme,\n    setTheme,\n    setColorScheme,\n    isDark,\n    toggleTheme\n  }\n\n  return (\n    <ThemeContext.Provider value={value}>\n      <div className={`min-h-screen transition-colors duration-300 ${\n        isDark ? themes.dark.background : themes.light.background\n      }`}>\n        {children}\n      </div>\n    </ThemeContext.Provider>\n  )\n}\n\nexport function useTheme() {\n  const context = useContext(ThemeContext)\n  if (context === undefined) {\n    throw new Error('useTheme must be used within a ThemeProvider')\n  }\n  return context\n}\n\n// Theme customizer component\nexport function ThemeCustomizer() {\n  const { theme, colorScheme, setTheme, setColorScheme, isDark } = useTheme()\n  const [isOpen, setIsOpen] = useState(false)\n\n  return (\n    <>\n      {/* Theme Toggle Button */}\n      <motion.button\n        onClick={() => setIsOpen(true)}\n        className={`fixed bottom-20 right-6 w-12 h-12 rounded-full shadow-lg z-40 flex items-center justify-center transition-colors ${\n          isDark ? 'bg-gray-800 text-white' : 'bg-white text-gray-900'\n        }`}\n        whileHover={{ scale: 1.1 }}\n        whileTap={{ scale: 0.9 }}\n      >\n        <motion.div\n          animate={{ rotate: isDark ? 180 : 0 }}\n          transition={{ duration: 0.3 }}\n        >\n          {isDark ? '🌙' : '☀️'}\n        </motion.div>\n      </motion.button>\n\n      {/* Theme Customizer Panel */}\n      <AnimatePresence>\n        {isOpen && (\n          <>\n            {/* Backdrop */}\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              exit={{ opacity: 0 }}\n              className=\"fixed inset-0 bg-black/50 z-50\"\n              onClick={() => setIsOpen(false)}\n            />\n\n            {/* Panel */}\n            <motion.div\n              initial={{ x: '100%' }}\n              animate={{ x: 0 }}\n              exit={{ x: '100%' }}\n              className={`fixed top-0 right-0 bottom-0 w-80 z-50 p-6 overflow-y-auto ${\n                isDark ? 'bg-gray-900 text-white' : 'bg-white text-gray-900'\n              }`}\n            >\n              <div className=\"flex items-center justify-between mb-6\">\n                <h2 className=\"text-xl font-bold\">Customize Theme</h2>\n                <motion.button\n                  onClick={() => setIsOpen(false)}\n                  className={`p-2 rounded-lg transition-colors ${\n                    isDark ? 'hover:bg-gray-800' : 'hover:bg-gray-100'\n                  }`}\n                  whileHover={{ scale: 1.1 }}\n                  whileTap={{ scale: 0.9 }}\n                >\n                  ✕\n                </motion.button>\n              </div>\n\n              {/* Theme Selection */}\n              <div className=\"mb-8\">\n                <h3 className=\"text-lg font-semibold mb-4\">Theme Mode</h3>\n                <div className=\"grid grid-cols-3 gap-2\">\n                  {(['light', 'dark', 'auto'] as Theme[]).map((themeOption) => (\n                    <motion.button\n                      key={themeOption}\n                      onClick={() => setTheme(themeOption)}\n                      className={`p-3 rounded-lg border-2 transition-all ${\n                        theme === themeOption\n                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'\n                          : isDark\n                          ? 'border-gray-700 hover:border-gray-600'\n                          : 'border-gray-200 hover:border-gray-300'\n                      }`}\n                      whileHover={{ scale: 1.02 }}\n                      whileTap={{ scale: 0.98 }}\n                    >\n                      <div className=\"text-2xl mb-1\">\n                        {themeOption === 'light' && '☀️'}\n                        {themeOption === 'dark' && '🌙'}\n                        {themeOption === 'auto' && '🔄'}\n                      </div>\n                      <div className=\"text-sm font-medium capitalize\">\n                        {themeOption}\n                      </div>\n                    </motion.button>\n                  ))}\n                </div>\n              </div>\n\n              {/* Color Scheme Selection */}\n              <div className=\"mb-8\">\n                <h3 className=\"text-lg font-semibold mb-4\">Color Scheme</h3>\n                <div className=\"grid grid-cols-3 gap-2\">\n                  {(Object.keys(colorSchemes) as ColorScheme[]).map((scheme) => (\n                    <motion.button\n                      key={scheme}\n                      onClick={() => setColorScheme(scheme)}\n                      className={`p-3 rounded-lg border-2 transition-all ${\n                        colorScheme === scheme\n                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'\n                          : isDark\n                          ? 'border-gray-700 hover:border-gray-600'\n                          : 'border-gray-200 hover:border-gray-300'\n                      }`}\n                      whileHover={{ scale: 1.02 }}\n                      whileTap={{ scale: 0.98 }}\n                    >\n                      <div className={`w-8 h-8 rounded-full mx-auto mb-2 ${\n                        scheme === 'blue' ? 'bg-blue-500' :\n                        scheme === 'purple' ? 'bg-purple-500' :\n                        scheme === 'green' ? 'bg-green-500' :\n                        scheme === 'orange' ? 'bg-orange-500' :\n                        scheme === 'red' ? 'bg-red-500' :\n                        'bg-pink-500'\n                      }`} />\n                      <div className=\"text-sm font-medium capitalize\">\n                        {scheme}\n                      </div>\n                    </motion.button>\n                  ))}\n                </div>\n              </div>\n\n              {/* Preview */}\n              <div className=\"mb-8\">\n                <h3 className=\"text-lg font-semibold mb-4\">Preview</h3>\n                <div className={`p-4 rounded-lg border ${\n                  isDark ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'\n                }`}>\n                  <div className=\"flex items-center gap-3 mb-3\">\n                    <div className={`w-10 h-10 rounded-full ${colorSchemes[colorScheme].primary}`} />\n                    <div>\n                      <div className=\"font-medium\">Sample Product</div>\n                      <div className={isDark ? 'text-gray-400' : 'text-gray-600'}>\n                        $299.99\n                      </div>\n                    </div>\n                  </div>\n                  <button className={`w-full py-2 px-4 rounded-lg text-white font-medium ${colorSchemes[colorScheme].primary}`}>\n                    Add to Cart\n                  </button>\n                </div>\n              </div>\n\n              {/* Reset Button */}\n              <motion.button\n                onClick={() => {\n                  setTheme('auto')\n                  setColorScheme('blue')\n                }}\n                className={`w-full py-3 px-4 rounded-lg border transition-colors ${\n                  isDark\n                    ? 'border-gray-700 hover:bg-gray-800'\n                    : 'border-gray-200 hover:bg-gray-50'\n                }`}\n                whileHover={{ scale: 1.02 }}\n                whileTap={{ scale: 0.98 }}\n              >\n                Reset to Default\n              </motion.button>\n            </motion.div>\n          </>\n        )}\n      </AnimatePresence>\n    </>\n  )\n}\n\n// Hook for theme-aware styling\nexport function useThemeClasses() {\n  const { isDark, colorScheme } = useTheme()\n  \n  const current = isDark ? themes.dark : themes.light\n  const colors = colorSchemes[colorScheme]\n  \n  return {\n    ...current,\n    ...colors,\n    isDark\n  }\n}\n\n// Animated theme transition component\nexport function ThemeTransition({ children }: { children: React.ReactNode }) {\n  const { isDark } = useTheme()\n  \n  return (\n    <motion.div\n      key={isDark ? 'dark' : 'light'}\n      initial={{ opacity: 0 }}\n      animate={{ opacity: 1 }}\n      exit={{ opacity: 0 }}\n      transition={{ duration: 0.3 }}\n    >\n      {children}\n    </motion.div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;AAAA;;;AAHA;;;AAiBA,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAgC;AAEjE,MAAM,SAAS;IACb,OAAO;QACL,YAAY;QACZ,SAAS;QACT,MAAM;QACN,eAAe;QACf,QAAQ;QACR,QAAQ;IACV;IACA,MAAM;QACJ,YAAY;QACZ,SAAS;QACT,MAAM;QACN,eAAe;QACf,QAAQ;QACR,QAAQ;IACV;AACF;AAEA,MAAM,eAAe;IACnB,MAAM;QACJ,SAAS;QACT,aAAa;QACb,WAAW;QACX,UAAU;IACZ;IACA,QAAQ;QACN,SAAS;QACT,aAAa;QACb,WAAW;QACX,UAAU;IACZ;IACA,OAAO;QACL,SAAS;QACT,aAAa;QACb,WAAW;QACX,UAAU;IACZ;IACA,QAAQ;QACN,SAAS;QACT,aAAa;QACb,WAAW;QACX,UAAU;IACZ;IACA,KAAK;QACH,SAAS;QACT,aAAa;QACb,WAAW;QACX,UAAU;IACZ;IACA,MAAM;QACJ,SAAS;QACT,aAAa;QACb,WAAW;QACX,UAAU;IACZ;AACF;AAEO,SAAS,cAAc,EAAE,QAAQ,EAAiC;;IACvE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS;IAC1C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,yBAAyB;YACzB,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,MAAM,mBAAmB,aAAa,OAAO,CAAC;YAE9C,IAAI,YAAY,SAAS;YACzB,IAAI,kBAAkB,eAAe;QACvC;kCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM;uDAAc;oBAClB,IAAI,eAAe;oBAEnB,IAAI,UAAU,QAAQ;wBACpB,eAAe;oBACjB,OAAO,IAAI,UAAU,QAAQ;wBAC3B,eAAe,OAAO,UAAU,CAAC,gCAAgC,OAAO;oBAC1E;oBAEA,UAAU;oBAEV,wBAAwB;oBACxB,IAAI,cAAc;wBAChB,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;oBACzC,OAAO;wBACL,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;oBAC5C;oBAEA,0BAA0B;oBAC1B,MAAM,iBAAiB,SAAS,aAAa,CAAC;oBAC9C,IAAI,gBAAgB;wBAClB,eAAe,YAAY,CAAC,WAAW,eAAe,YAAY;oBACpE;gBACF;;YAEA;YAEA,kCAAkC;YAClC,MAAM,aAAa,OAAO,UAAU,CAAC;YACrC,MAAM;wDAAe;oBACnB,IAAI,UAAU,QAAQ;wBACpB;oBACF;gBACF;;YAEA,WAAW,gBAAgB,CAAC,UAAU;YACtC;2CAAO,IAAM,WAAW,mBAAmB,CAAC,UAAU;;QACxD;kCAAG;QAAC;KAAM;IAEV,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,mBAAmB;YACnB,aAAa,OAAO,CAAC,SAAS;YAC9B,aAAa,OAAO,CAAC,eAAe;QACtC;kCAAG;QAAC;QAAO;KAAY;IAEvB,MAAM,cAAc;QAClB,SAAS,CAAA;YACP,IAAI,YAAY,SAAS,OAAO;YAChC,IAAI,YAAY,QAAQ,OAAO;YAC/B,OAAO;QACT;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,aAAa,QAAQ;QAAC,OAAO;kBAC5B,cAAA,6LAAC;YAAI,WAAW,CAAC,4CAA4C,EAC3D,SAAS,OAAO,IAAI,CAAC,UAAU,GAAG,OAAO,KAAK,CAAC,UAAU,EACzD;sBACC;;;;;;;;;;;AAIT;GAtFgB;KAAA;AAwFT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB;AAST,SAAS;;IACd,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,QAAQ,EAAE,cAAc,EAAE,MAAM,EAAE,GAAG;IACjE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,qBACE;;0BAEE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS,IAAM,UAAU;gBACzB,WAAW,CAAC,iHAAiH,EAC3H,SAAS,2BAA2B,0BACpC;gBACF,YAAY;oBAAE,OAAO;gBAAI;gBACzB,UAAU;oBAAE,OAAO;gBAAI;0BAEvB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,QAAQ,SAAS,MAAM;oBAAE;oBACpC,YAAY;wBAAE,UAAU;oBAAI;8BAE3B,SAAS,OAAO;;;;;;;;;;;0BAKrB,6LAAC,4LAAA,CAAA,kBAAe;0BACb,wBACC;;sCAEE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,MAAM;gCAAE,SAAS;4BAAE;4BACnB,WAAU;4BACV,SAAS,IAAM,UAAU;;;;;;sCAI3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,GAAG;4BAAO;4BACrB,SAAS;gCAAE,GAAG;4BAAE;4BAChB,MAAM;gCAAE,GAAG;4BAAO;4BAClB,WAAW,CAAC,2DAA2D,EACrE,SAAS,2BAA2B,0BACpC;;8CAEF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAoB;;;;;;sDAClC,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,SAAS,IAAM,UAAU;4CACzB,WAAW,CAAC,iCAAiC,EAC3C,SAAS,sBAAsB,qBAC/B;4CACF,YAAY;gDAAE,OAAO;4CAAI;4CACzB,UAAU;gDAAE,OAAO;4CAAI;sDACxB;;;;;;;;;;;;8CAMH,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAI,WAAU;sDACZ,AAAC;gDAAC;gDAAS;gDAAQ;6CAAO,CAAa,GAAG,CAAC,CAAC,4BAC3C,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oDAEZ,SAAS,IAAM,SAAS;oDACxB,WAAW,CAAC,uCAAuC,EACjD,UAAU,cACN,mDACA,SACA,0CACA,yCACJ;oDACF,YAAY;wDAAE,OAAO;oDAAK;oDAC1B,UAAU;wDAAE,OAAO;oDAAK;;sEAExB,6LAAC;4DAAI,WAAU;;gEACZ,gBAAgB,WAAW;gEAC3B,gBAAgB,UAAU;gEAC1B,gBAAgB,UAAU;;;;;;;sEAE7B,6LAAC;4DAAI,WAAU;sEACZ;;;;;;;mDAlBE;;;;;;;;;;;;;;;;8CA0Bb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAI,WAAU;sDACZ,AAAC,OAAO,IAAI,CAAC,cAAgC,GAAG,CAAC,CAAC,uBACjD,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oDAEZ,SAAS,IAAM,eAAe;oDAC9B,WAAW,CAAC,uCAAuC,EACjD,gBAAgB,SACZ,mDACA,SACA,0CACA,yCACJ;oDACF,YAAY;wDAAE,OAAO;oDAAK;oDAC1B,UAAU;wDAAE,OAAO;oDAAK;;sEAExB,6LAAC;4DAAI,WAAW,CAAC,kCAAkC,EACjD,WAAW,SAAS,gBACpB,WAAW,WAAW,kBACtB,WAAW,UAAU,iBACrB,WAAW,WAAW,kBACtB,WAAW,QAAQ,eACnB,eACA;;;;;;sEACF,6LAAC;4DAAI,WAAU;sEACZ;;;;;;;mDArBE;;;;;;;;;;;;;;;;8CA6Bb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAI,WAAW,CAAC,sBAAsB,EACrC,SAAS,gCAAgC,8BACzC;;8DACA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAW,CAAC,uBAAuB,EAAE,YAAY,CAAC,YAAY,CAAC,OAAO,EAAE;;;;;;sEAC7E,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;8EAAc;;;;;;8EAC7B,6LAAC;oEAAI,WAAW,SAAS,kBAAkB;8EAAiB;;;;;;;;;;;;;;;;;;8DAKhE,6LAAC;oDAAO,WAAW,CAAC,mDAAmD,EAAE,YAAY,CAAC,YAAY,CAAC,OAAO,EAAE;8DAAE;;;;;;;;;;;;;;;;;;8CAOlH,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS;wCACP,SAAS;wCACT,eAAe;oCACjB;oCACA,WAAW,CAAC,qDAAqD,EAC/D,SACI,sCACA,oCACJ;oCACF,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;8CACzB;;;;;;;;;;;;;;;;;;;;;AASf;IAvKgB;;QACmD;;;MADnD;AA0KT,SAAS;;IACd,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG;IAEhC,MAAM,UAAU,SAAS,OAAO,IAAI,GAAG,OAAO,KAAK;IACnD,MAAM,SAAS,YAAY,CAAC,YAAY;IAExC,OAAO;QACL,GAAG,OAAO;QACV,GAAG,MAAM;QACT;IACF;AACF;IAXgB;;QACkB;;;AAa3B,SAAS,gBAAgB,EAAE,QAAQ,EAAiC;;IACzE,MAAM,EAAE,MAAM,EAAE,GAAG;IAEnB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QAET,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,MAAM;YAAE,SAAS;QAAE;QACnB,YAAY;YAAE,UAAU;QAAI;kBAE3B;OANI,SAAS,SAAS;;;;;AAS7B;IAdgB;;QACK;;;MADL", "debugId": null}}, {"offset": {"line": 1141, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Bayt/bayt-web/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPrice(price: number, currency: string = 'USD') {\n  const formatters = {\n    USD: new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }),\n    EUR: new Intl.NumberFormat('en-EU', { style: 'currency', currency: 'EUR' }),\n    TRY: new Intl.NumberFormat('tr-TR', { style: 'currency', currency: 'TRY' }),\n    SYP: new Intl.NumberFormat('ar-SY', { style: 'currency', currency: 'SYP' }),\n  }\n  \n  const formatter = formatters[currency as keyof typeof formatters] || formatters.USD\n  return formatter.format(price)\n}\n\nexport function formatDate(date: string | Date, locale: string = 'en-US') {\n  const dateObj = typeof date === 'string' ? new Date(date) : date\n  return new Intl.DateTimeFormat(locale, {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  }).format(dateObj)\n}\n\nexport function formatRelativeTime(date: string | Date, locale: string = 'en-US') {\n  const dateObj = typeof date === 'string' ? new Date(date) : date\n  const now = new Date()\n  const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000)\n  \n  if (diffInSeconds < 60) return 'Just now'\n  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`\n  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`\n  if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)} days ago`\n  \n  return formatDate(dateObj, locale)\n}\n\nexport function slugify(text: string) {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '')\n    .replace(/[\\s_-]+/g, '-')\n    .replace(/^-+|-+$/g, '')\n}\n\nexport function truncateText(text: string, maxLength: number) {\n  if (text.length <= maxLength) return text\n  return text.slice(0, maxLength) + '...'\n}\n\nexport function getImageUrl(path: string | null, bucket: string = 'products') {\n  if (!path) return '/placeholder-image.jpg'\n  \n  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL\n  return `${supabaseUrl}/storage/v1/object/public/${bucket}/${path}`\n}\n\nexport function validateFileType(file: File, allowedTypes: string[]) {\n  return allowedTypes.includes(file.type)\n}\n\nexport function validateFileSize(file: File, maxSizeInBytes: number) {\n  return file.size <= maxSizeInBytes\n}\n\nexport function generateUniqueFileName(originalName: string) {\n  const timestamp = Date.now()\n  const random = Math.random().toString(36).substring(2)\n  const extension = originalName.split('.').pop()\n  return `${timestamp}-${random}.${extension}`\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAyDsB;AAzDtB;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa,EAAE,WAAmB,KAAK;IACjE,MAAM,aAAa;QACjB,KAAK,IAAI,KAAK,YAAY,CAAC,SAAS;YAAE,OAAO;YAAY,UAAU;QAAM;QACzE,KAAK,IAAI,KAAK,YAAY,CAAC,SAAS;YAAE,OAAO;YAAY,UAAU;QAAM;QACzE,KAAK,IAAI,KAAK,YAAY,CAAC,SAAS;YAAE,OAAO;YAAY,UAAU;QAAM;QACzE,KAAK,IAAI,KAAK,YAAY,CAAC,SAAS;YAAE,OAAO;YAAY,UAAU;QAAM;IAC3E;IAEA,MAAM,YAAY,UAAU,CAAC,SAAoC,IAAI,WAAW,GAAG;IACnF,OAAO,UAAU,MAAM,CAAC;AAC1B;AAEO,SAAS,WAAW,IAAmB,EAAE,SAAiB,OAAO;IACtE,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,IAAI,KAAK,cAAc,CAAC,QAAQ;QACrC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,mBAAmB,IAAmB,EAAE,SAAiB,OAAO;IAC9E,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,QAAQ,OAAO,EAAE,IAAI;IAEvE,IAAI,gBAAgB,IAAI,OAAO;IAC/B,IAAI,gBAAgB,MAAM,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,IAAI,YAAY,CAAC;IAChF,IAAI,gBAAgB,OAAO,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,MAAM,UAAU,CAAC;IACjF,IAAI,gBAAgB,QAAQ,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,OAAO,SAAS,CAAC;IAElF,OAAO,WAAW,SAAS;AAC7B;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,aAAa,IACrB,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,YAAY;AACzB;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,SAAS,YAAY,IAAmB,EAAE,SAAiB,UAAU;IAC1E,IAAI,CAAC,MAAM,OAAO;IAElB,MAAM;IACN,OAAO,GAAG,YAAY,0BAA0B,EAAE,OAAO,CAAC,EAAE,MAAM;AACpE;AAEO,SAAS,iBAAiB,IAAU,EAAE,YAAsB;IACjE,OAAO,aAAa,QAAQ,CAAC,KAAK,IAAI;AACxC;AAEO,SAAS,iBAAiB,IAAU,EAAE,cAAsB;IACjE,OAAO,KAAK,IAAI,IAAI;AACtB;AAEO,SAAS,uBAAuB,YAAoB;IACzD,MAAM,YAAY,KAAK,GAAG;IAC1B,MAAM,SAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC;IACpD,MAAM,YAAY,aAAa,KAAK,CAAC,KAAK,GAAG;IAC7C,OAAO,GAAG,UAAU,CAAC,EAAE,OAAO,CAAC,EAAE,WAAW;AAC9C", "debugId": null}}, {"offset": {"line": 1234, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Bayt/bayt-web/src/components/ui/Button.tsx"], "sourcesContent": ["import { forwardRef } from 'react'\nimport { cva, type VariantProps } from 'class-variance-authority'\nimport { cn } from '@/lib/utils'\nimport { Loader2 } from 'lucide-react'\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background',\n  {\n    variants: {\n      variant: {\n        default: 'bg-blue-600 text-white hover:bg-blue-700',\n        destructive: 'bg-red-600 text-white hover:bg-red-700',\n        outline: 'border border-gray-300 bg-white hover:bg-gray-50 text-gray-900',\n        secondary: 'bg-gray-100 text-gray-900 hover:bg-gray-200',\n        ghost: 'hover:bg-gray-100 text-gray-900',\n        link: 'underline-offset-4 hover:underline text-blue-600',\n      },\n      size: {\n        default: 'h-10 py-2 px-4',\n        sm: 'h-9 px-3 rounded-md',\n        lg: 'h-11 px-8 rounded-md',\n        icon: 'h-10 w-10',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  loading?: boolean\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, loading, children, disabled, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n        {children}\n      </button>\n    )\n  }\n)\nButton.displayName = 'Button'\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,wQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OACtB,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACpE,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBAAW,6LAAC,oNAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YAC9B;;;;;;;AAGP;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1311, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Bayt/bayt-web/src/components/ui/ErrorBoundary.tsx"], "sourcesContent": ["'use client'\n\nimport React, { Component, ErrorInfo, ReactNode } from 'react'\nimport { motion } from 'framer-motion'\nimport { AlertTriangle, RefreshCw, Home, Bug } from 'lucide-react'\nimport { Button } from '@/components/ui/Button'\n\ninterface Props {\n  children: ReactNode\n  fallback?: ReactNode\n  onError?: (error: Error, errorInfo: ErrorInfo) => void\n}\n\ninterface State {\n  hasError: boolean\n  error?: Error\n  errorInfo?: ErrorInfo\n}\n\nexport class ErrorBoundary extends Component<Props, State> {\n  constructor(props: Props) {\n    super(props)\n    this.state = { hasError: false }\n  }\n\n  static getDerivedStateFromError(error: Error): State {\n    return { hasError: true, error }\n  }\n\n  componentDidCatch(error: Error, errorInfo: ErrorInfo) {\n    console.error('Error caught by boundary:', error, errorInfo)\n    \n    this.setState({ error, errorInfo })\n    \n    // Call custom error handler\n    this.props.onError?.(error, errorInfo)\n    \n    // Log to error reporting service\n    this.logErrorToService(error, errorInfo)\n  }\n\n  logErrorToService = (error: Error, errorInfo: ErrorInfo) => {\n    // In production, send to error reporting service like Sentry\n    const errorData = {\n      message: error.message,\n      stack: error.stack,\n      componentStack: errorInfo.componentStack,\n      timestamp: new Date().toISOString(),\n      userAgent: navigator.userAgent,\n      url: window.location.href\n    }\n    \n    console.log('Error logged:', errorData)\n    \n    // Example: Send to analytics\n    if (typeof window !== 'undefined' && (window as any).gtag) {\n      (window as any).gtag('event', 'exception', {\n        description: error.message,\n        fatal: false\n      })\n    }\n  }\n\n  handleRetry = () => {\n    this.setState({ hasError: false, error: undefined, errorInfo: undefined })\n  }\n\n  handleGoHome = () => {\n    window.location.href = '/'\n  }\n\n  render() {\n    if (this.state.hasError) {\n      if (this.props.fallback) {\n        return this.props.fallback\n      }\n\n      return (\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-blue-50 p-4\"\n        >\n          <div className=\"max-w-md w-full bg-white rounded-2xl shadow-xl p-8 text-center\">\n            <motion.div\n              initial={{ scale: 0 }}\n              animate={{ scale: 1 }}\n              transition={{ delay: 0.2 }}\n              className=\"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6\"\n            >\n              <AlertTriangle className=\"w-8 h-8 text-red-500\" />\n            </motion.div>\n\n            <motion.h1\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ delay: 0.3 }}\n              className=\"text-2xl font-bold text-gray-900 mb-4\"\n            >\n              Oops! Something went wrong\n            </motion.h1>\n\n            <motion.p\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ delay: 0.4 }}\n              className=\"text-gray-600 mb-6\"\n            >\n              We're sorry for the inconvenience. The page encountered an unexpected error.\n            </motion.p>\n\n            {process.env.NODE_ENV === 'development' && this.state.error && (\n              <motion.details\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                transition={{ delay: 0.5 }}\n                className=\"text-left bg-gray-50 rounded-lg p-4 mb-6\"\n              >\n                <summary className=\"cursor-pointer text-sm font-medium text-gray-700 mb-2\">\n                  <Bug className=\"w-4 h-4 inline mr-2\" />\n                  Error Details (Development)\n                </summary>\n                <pre className=\"text-xs text-red-600 overflow-auto max-h-32\">\n                  {this.state.error.message}\n                  {'\\n\\n'}\n                  {this.state.error.stack}\n                </pre>\n              </motion.details>\n            )}\n\n            <motion.div\n              initial={{ opacity: 0, y: 10 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.6 }}\n              className=\"flex flex-col sm:flex-row gap-3\"\n            >\n              <Button\n                onClick={this.handleRetry}\n                className=\"flex-1 bg-blue-600 hover:bg-blue-700\"\n              >\n                <RefreshCw className=\"w-4 h-4 mr-2\" />\n                Try Again\n              </Button>\n              <Button\n                onClick={this.handleGoHome}\n                variant=\"outline\"\n                className=\"flex-1\"\n              >\n                <Home className=\"w-4 h-4 mr-2\" />\n                Go Home\n              </Button>\n            </motion.div>\n\n            <motion.p\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ delay: 0.7 }}\n              className=\"text-sm text-gray-500 mt-6\"\n            >\n              If the problem persists, please contact our support team.\n            </motion.p>\n          </div>\n        </motion.div>\n      )\n    }\n\n    return this.props.children\n  }\n}\n\n// Hook for error handling\nexport function useErrorHandler() {\n  const handleError = (error: Error, errorInfo?: any) => {\n    console.error('Error handled:', error, errorInfo)\n    \n    // Log to error service\n    const errorData = {\n      message: error.message,\n      stack: error.stack,\n      timestamp: new Date().toISOString(),\n      userAgent: navigator.userAgent,\n      url: window.location.href,\n      ...errorInfo\n    }\n    \n    // Send to analytics\n    if (typeof window !== 'undefined' && (window as any).gtag) {\n      (window as any).gtag('event', 'exception', {\n        description: error.message,\n        fatal: false\n      })\n    }\n  }\n\n  return { handleError }\n}\n\n// Async error boundary for handling promise rejections\nexport function AsyncErrorBoundary({ children }: { children: ReactNode }) {\n  React.useEffect(() => {\n    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {\n      console.error('Unhandled promise rejection:', event.reason)\n      \n      // Log to error service\n      const errorData = {\n        type: 'unhandled_promise_rejection',\n        reason: event.reason?.toString() || 'Unknown error',\n        timestamp: new Date().toISOString(),\n        url: window.location.href\n      }\n      \n      console.log('Promise rejection logged:', errorData)\n    }\n\n    window.addEventListener('unhandledrejection', handleUnhandledRejection)\n    \n    return () => {\n      window.removeEventListener('unhandledrejection', handleUnhandledRejection)\n    }\n  }, [])\n\n  return <>{children}</>\n}\n\n// Component-specific error boundary\nexport function ComponentErrorBoundary({ \n  children, \n  componentName,\n  fallback \n}: { \n  children: ReactNode\n  componentName: string\n  fallback?: ReactNode\n}) {\n  return (\n    <ErrorBoundary\n      fallback={fallback || (\n        <div className=\"p-4 bg-red-50 border border-red-200 rounded-lg\">\n          <div className=\"flex items-center gap-2 text-red-700\">\n            <AlertTriangle className=\"w-4 h-4\" />\n            <span className=\"font-medium\">Error in {componentName}</span>\n          </div>\n          <p className=\"text-sm text-red-600 mt-1\">\n            This component failed to load. Please refresh the page.\n          </p>\n        </div>\n      )}\n      onError={(error, errorInfo) => {\n        console.error(`Error in ${componentName}:`, error, errorInfo)\n      }}\n    >\n      {children}\n    </ErrorBoundary>\n  )\n}\n\n// Network error boundary for API calls\nexport function NetworkErrorBoundary({ children }: { children: ReactNode }) {\n  const [hasNetworkError, setHasNetworkError] = React.useState(false)\n\n  React.useEffect(() => {\n    const handleOnline = () => setHasNetworkError(false)\n    const handleOffline = () => setHasNetworkError(true)\n\n    window.addEventListener('online', handleOnline)\n    window.addEventListener('offline', handleOffline)\n\n    return () => {\n      window.removeEventListener('online', handleOnline)\n      window.removeEventListener('offline', handleOffline)\n    }\n  }, [])\n\n  if (hasNetworkError) {\n    return (\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        className=\"p-4 bg-yellow-50 border border-yellow-200 rounded-lg\"\n      >\n        <div className=\"flex items-center gap-2 text-yellow-700\">\n          <AlertTriangle className=\"w-4 h-4\" />\n          <span className=\"font-medium\">Network Error</span>\n        </div>\n        <p className=\"text-sm text-yellow-600 mt-1\">\n          You're currently offline. Some features may not work properly.\n        </p>\n      </motion.div>\n    )\n  }\n\n  return <>{children}</>\n}\n\n// Global error handler setup\nexport function setupGlobalErrorHandling() {\n  // Handle uncaught errors\n  window.addEventListener('error', (event) => {\n    console.error('Global error:', event.error)\n    \n    const errorData = {\n      type: 'javascript_error',\n      message: event.message,\n      filename: event.filename,\n      lineno: event.lineno,\n      colno: event.colno,\n      stack: event.error?.stack,\n      timestamp: new Date().toISOString(),\n      userAgent: navigator.userAgent,\n      url: window.location.href\n    }\n    \n    console.log('Global error logged:', errorData)\n  })\n\n  // Handle unhandled promise rejections\n  window.addEventListener('unhandledrejection', (event) => {\n    console.error('Unhandled promise rejection:', event.reason)\n    \n    const errorData = {\n      type: 'unhandled_promise_rejection',\n      reason: event.reason?.toString() || 'Unknown error',\n      timestamp: new Date().toISOString(),\n      url: window.location.href\n    }\n    \n    console.log('Promise rejection logged:', errorData)\n  })\n}\n"], "names": [], "mappings": ";;;;;;;;AA+Ga;;AA7Gb;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;AALA;;;;;AAmBO,MAAM,sBAAsB,6JAAA,CAAA,YAAS;IAC1C,YAAY,KAAY,CAAE;QACxB,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;IAEA,OAAO,yBAAyB,KAAY,EAAS;QACnD,OAAO;YAAE,UAAU;YAAM;QAAM;IACjC;IAEA,kBAAkB,KAAY,EAAE,SAAoB,EAAE;QACpD,QAAQ,KAAK,CAAC,6BAA6B,OAAO;QAElD,IAAI,CAAC,QAAQ,CAAC;YAAE;YAAO;QAAU;QAEjC,4BAA4B;QAC5B,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO;QAE5B,iCAAiC;QACjC,IAAI,CAAC,iBAAiB,CAAC,OAAO;IAChC;IAEA,oBAAoB,CAAC,OAAc;QACjC,6DAA6D;QAC7D,MAAM,YAAY;YAChB,SAAS,MAAM,OAAO;YACtB,OAAO,MAAM,KAAK;YAClB,gBAAgB,UAAU,cAAc;YACxC,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,UAAU,SAAS;YAC9B,KAAK,OAAO,QAAQ,CAAC,IAAI;QAC3B;QAEA,QAAQ,GAAG,CAAC,iBAAiB;QAE7B,6BAA6B;QAC7B,IAAI,aAAkB,eAAe,AAAC,OAAe,IAAI,EAAE;YACxD,OAAe,IAAI,CAAC,SAAS,aAAa;gBACzC,aAAa,MAAM,OAAO;gBAC1B,OAAO;YACT;QACF;IACF,EAAC;IAED,cAAc;QACZ,IAAI,CAAC,QAAQ,CAAC;YAAE,UAAU;YAAO,OAAO;YAAW,WAAW;QAAU;IAC1E,EAAC;IAED,eAAe;QACb,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB,EAAC;IAED,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACvB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC5B;YAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,WAAU;0BAEV,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,OAAO;4BAAE;4BACpB,SAAS;gCAAE,OAAO;4BAAE;4BACpB,YAAY;gCAAE,OAAO;4BAAI;4BACzB,WAAU;sCAEV,cAAA,6LAAC,2NAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;;;;;;sCAG3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,YAAY;gCAAE,OAAO;4BAAI;4BACzB,WAAU;sCACX;;;;;;sCAID,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,YAAY;gCAAE,OAAO;4BAAI;4BACzB,WAAU;sCACX;;;;;;wBAIA,oDAAyB,iBAAiB,IAAI,CAAC,KAAK,CAAC,KAAK,kBACzD,6LAAC,6LAAA,CAAA,SAAM,CAAC,OAAO;4BACb,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,YAAY;gCAAE,OAAO;4BAAI;4BACzB,WAAU;;8CAEV,6LAAC;oCAAQ,WAAU;;sDACjB,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCAAwB;;;;;;;8CAGzC,6LAAC;oCAAI,WAAU;;wCACZ,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO;wCACxB;wCACA,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK;;;;;;;;;;;;;sCAK7B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;4BACzB,WAAU;;8CAEV,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS,IAAI,CAAC,WAAW;oCACzB,WAAU;;sDAEV,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGxC,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS,IAAI,CAAC,YAAY;oCAC1B,SAAQ;oCACR,WAAU;;sDAEV,6LAAC,sMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAKrC,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,YAAY;gCAAE,OAAO;4BAAI;4BACzB,WAAU;sCACX;;;;;;;;;;;;;;;;;QAMT;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAC,OAAc;QACjC,QAAQ,KAAK,CAAC,kBAAkB,OAAO;QAEvC,uBAAuB;QACvB,MAAM,YAAY;YAChB,SAAS,MAAM,OAAO;YACtB,OAAO,MAAM,KAAK;YAClB,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,UAAU,SAAS;YAC9B,KAAK,OAAO,QAAQ,CAAC,IAAI;YACzB,GAAG,SAAS;QACd;QAEA,oBAAoB;QACpB,IAAI,aAAkB,eAAe,AAAC,OAAe,IAAI,EAAE;YACxD,OAAe,IAAI,CAAC,SAAS,aAAa;gBACzC,aAAa,MAAM,OAAO;gBAC1B,OAAO;YACT;QACF;IACF;IAEA,OAAO;QAAE;IAAY;AACvB;AAGO,SAAS,mBAAmB,EAAE,QAAQ,EAA2B;;IACtE,6JAAA,CAAA,UAAK,CAAC,SAAS;wCAAC;YACd,MAAM;yEAA2B,CAAC;oBAChC,QAAQ,KAAK,CAAC,gCAAgC,MAAM,MAAM;oBAE1D,uBAAuB;oBACvB,MAAM,YAAY;wBAChB,MAAM;wBACN,QAAQ,MAAM,MAAM,EAAE,cAAc;wBACpC,WAAW,IAAI,OAAO,WAAW;wBACjC,KAAK,OAAO,QAAQ,CAAC,IAAI;oBAC3B;oBAEA,QAAQ,GAAG,CAAC,6BAA6B;gBAC3C;;YAEA,OAAO,gBAAgB,CAAC,sBAAsB;YAE9C;gDAAO;oBACL,OAAO,mBAAmB,CAAC,sBAAsB;gBACnD;;QACF;uCAAG,EAAE;IAEL,qBAAO;kBAAG;;AACZ;GAxBgB;KAAA;AA2BT,SAAS,uBAAuB,EACrC,QAAQ,EACR,aAAa,EACb,QAAQ,EAKT;IACC,qBACE,6LAAC;QACC,UAAU,0BACR,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,2NAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;sCACzB,6LAAC;4BAAK,WAAU;;gCAAc;gCAAU;;;;;;;;;;;;;8BAE1C,6LAAC;oBAAE,WAAU;8BAA4B;;;;;;;;;;;;QAK7C,SAAS,CAAC,OAAO;YACf,QAAQ,KAAK,CAAC,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC,EAAE,OAAO;QACrD;kBAEC;;;;;;AAGP;MA7BgB;AAgCT,SAAS,qBAAqB,EAAE,QAAQ,EAA2B;;IACxE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAE7D,6JAAA,CAAA,UAAK,CAAC,SAAS;0CAAC;YACd,MAAM;+DAAe,IAAM,mBAAmB;;YAC9C,MAAM;gEAAgB,IAAM,mBAAmB;;YAE/C,OAAO,gBAAgB,CAAC,UAAU;YAClC,OAAO,gBAAgB,CAAC,WAAW;YAEnC;kDAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;oBACrC,OAAO,mBAAmB,CAAC,WAAW;gBACxC;;QACF;yCAAG,EAAE;IAEL,IAAI,iBAAiB;QACnB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC7B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,WAAU;;8BAEV,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,2NAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;sCACzB,6LAAC;4BAAK,WAAU;sCAAc;;;;;;;;;;;;8BAEhC,6LAAC;oBAAE,WAAU;8BAA+B;;;;;;;;;;;;IAKlD;IAEA,qBAAO;kBAAG;;AACZ;IAnCgB;MAAA;AAsCT,SAAS;IACd,yBAAyB;IACzB,OAAO,gBAAgB,CAAC,SAAS,CAAC;QAChC,QAAQ,KAAK,CAAC,iBAAiB,MAAM,KAAK;QAE1C,MAAM,YAAY;YAChB,MAAM;YACN,SAAS,MAAM,OAAO;YACtB,UAAU,MAAM,QAAQ;YACxB,QAAQ,MAAM,MAAM;YACpB,OAAO,MAAM,KAAK;YAClB,OAAO,MAAM,KAAK,EAAE;YACpB,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,UAAU,SAAS;YAC9B,KAAK,OAAO,QAAQ,CAAC,IAAI;QAC3B;QAEA,QAAQ,GAAG,CAAC,wBAAwB;IACtC;IAEA,sCAAsC;IACtC,OAAO,gBAAgB,CAAC,sBAAsB,CAAC;QAC7C,QAAQ,KAAK,CAAC,gCAAgC,MAAM,MAAM;QAE1D,MAAM,YAAY;YAChB,MAAM;YACN,QAAQ,MAAM,MAAM,EAAE,cAAc;YACpC,WAAW,IAAI,OAAO,WAAW;YACjC,KAAK,OAAO,QAAQ,CAAC,IAAI;QAC3B;QAEA,QAAQ,GAAG,CAAC,6BAA6B;IAC3C;AACF", "debugId": null}}, {"offset": {"line": 1831, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Bayt/bayt-web/src/components/pwa/PWAInstaller.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { Download, X, Smartphone, Monitor, Share, Plus } from 'lucide-react'\nimport { Button } from '@/components/ui/Button'\n\ninterface BeforeInstallPromptEvent extends Event {\n  readonly platforms: string[]\n  readonly userChoice: Promise<{\n    outcome: 'accepted' | 'dismissed'\n    platform: string\n  }>\n  prompt(): Promise<void>\n}\n\nexport function PWAInstaller() {\n  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null)\n  const [showInstallPrompt, setShowInstallPrompt] = useState(false)\n  const [isInstalled, setIsInstalled] = useState(false)\n  const [isIOS, setIsIOS] = useState(false)\n  const [isStandalone, setIsStandalone] = useState(false)\n\n  useEffect(() => {\n    // Check if app is already installed\n    setIsStandalone(window.matchMedia('(display-mode: standalone)').matches)\n    \n    // Check if iOS\n    setIsIOS(/iPad|iPhone|iPod/.test(navigator.userAgent))\n    \n    // Check if already installed\n    if ('getInstalledRelatedApps' in navigator) {\n      (navigator as any).getInstalledRelatedApps().then((relatedApps: any[]) => {\n        setIsInstalled(relatedApps.length > 0)\n      })\n    }\n\n    const handleBeforeInstallPrompt = (e: Event) => {\n      e.preventDefault()\n      setDeferredPrompt(e as BeforeInstallPromptEvent)\n      \n      // Show install prompt after a delay\n      setTimeout(() => {\n        if (!isStandalone && !isInstalled) {\n          setShowInstallPrompt(true)\n        }\n      }, 5000)\n    }\n\n    const handleAppInstalled = () => {\n      setIsInstalled(true)\n      setShowInstallPrompt(false)\n      setDeferredPrompt(null)\n    }\n\n    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)\n    window.addEventListener('appinstalled', handleAppInstalled)\n\n    return () => {\n      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)\n      window.removeEventListener('appinstalled', handleAppInstalled)\n    }\n  }, [isStandalone, isInstalled])\n\n  const handleInstallClick = async () => {\n    if (!deferredPrompt) return\n\n    deferredPrompt.prompt()\n    const { outcome } = await deferredPrompt.userChoice\n    \n    if (outcome === 'accepted') {\n      console.log('User accepted the install prompt')\n    } else {\n      console.log('User dismissed the install prompt')\n    }\n    \n    setDeferredPrompt(null)\n    setShowInstallPrompt(false)\n  }\n\n  const handleDismiss = () => {\n    setShowInstallPrompt(false)\n    // Don't show again for 24 hours\n    localStorage.setItem('pwa-install-dismissed', Date.now().toString())\n  }\n\n  // Don't show if already installed or dismissed recently\n  if (isInstalled || isStandalone) return null\n\n  const dismissedTime = localStorage.getItem('pwa-install-dismissed')\n  if (dismissedTime && Date.now() - parseInt(dismissedTime) < 24 * 60 * 60 * 1000) {\n    return null\n  }\n\n  return (\n    <AnimatePresence>\n      {showInstallPrompt && (\n        <motion.div\n          initial={{ opacity: 0, y: 100 }}\n          animate={{ opacity: 1, y: 0 }}\n          exit={{ opacity: 0, y: 100 }}\n          className=\"fixed bottom-4 left-4 right-4 z-50 md:left-auto md:right-4 md:w-96\"\n        >\n          <div className=\"bg-white rounded-2xl shadow-2xl border border-gray-200 p-6\">\n            <div className=\"flex items-start justify-between mb-4\">\n              <div className=\"flex items-center gap-3\">\n                <div className=\"w-12 h-12 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center\">\n                  <Smartphone className=\"w-6 h-6 text-white\" />\n                </div>\n                <div>\n                  <h3 className=\"font-semibold text-gray-900\">Install Bayt App</h3>\n                  <p className=\"text-sm text-gray-600\">Get the full experience</p>\n                </div>\n              </div>\n              <motion.button\n                onClick={handleDismiss}\n                className=\"p-1 text-gray-400 hover:text-gray-600 transition-colors\"\n                whileHover={{ scale: 1.1 }}\n                whileTap={{ scale: 0.9 }}\n              >\n                <X className=\"w-5 h-5\" />\n              </motion.button>\n            </div>\n\n            <div className=\"space-y-3 mb-6\">\n              <div className=\"flex items-center gap-3 text-sm text-gray-600\">\n                <div className=\"w-5 h-5 bg-green-100 rounded-full flex items-center justify-center\">\n                  <div className=\"w-2 h-2 bg-green-500 rounded-full\" />\n                </div>\n                <span>Works offline</span>\n              </div>\n              <div className=\"flex items-center gap-3 text-sm text-gray-600\">\n                <div className=\"w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center\">\n                  <div className=\"w-2 h-2 bg-blue-500 rounded-full\" />\n                </div>\n                <span>Fast and reliable</span>\n              </div>\n              <div className=\"flex items-center gap-3 text-sm text-gray-600\">\n                <div className=\"w-5 h-5 bg-purple-100 rounded-full flex items-center justify-center\">\n                  <div className=\"w-2 h-2 bg-purple-500 rounded-full\" />\n                </div>\n                <span>Native app experience</span>\n              </div>\n            </div>\n\n            {isIOS ? (\n              <IOSInstallInstructions />\n            ) : (\n              <div className=\"flex gap-3\">\n                <Button\n                  onClick={handleInstallClick}\n                  className=\"flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700\"\n                  disabled={!deferredPrompt}\n                >\n                  <Download className=\"w-4 h-4 mr-2\" />\n                  Install App\n                </Button>\n                <Button\n                  onClick={handleDismiss}\n                  variant=\"outline\"\n                  className=\"px-4\"\n                >\n                  Later\n                </Button>\n              </div>\n            )}\n          </div>\n        </motion.div>\n      )}\n    </AnimatePresence>\n  )\n}\n\nfunction IOSInstallInstructions() {\n  const [step, setStep] = useState(0)\n\n  const steps = [\n    {\n      icon: Share,\n      text: 'Tap the Share button',\n      detail: 'Look for the share icon in your browser'\n    },\n    {\n      icon: Plus,\n      text: 'Add to Home Screen',\n      detail: 'Scroll down and tap \"Add to Home Screen\"'\n    },\n    {\n      icon: Download,\n      text: 'Confirm Installation',\n      detail: 'Tap \"Add\" to install the app'\n    }\n  ]\n\n  return (\n    <div className=\"space-y-4\">\n      <p className=\"text-sm text-gray-600 mb-4\">\n        To install this app on your iPhone:\n      </p>\n      \n      <div className=\"space-y-3\">\n        {steps.map((stepItem, index) => {\n          const Icon = stepItem.icon\n          return (\n            <motion.div\n              key={index}\n              className={`flex items-center gap-3 p-3 rounded-lg transition-colors ${\n                index === step ? 'bg-blue-50 border border-blue-200' : 'bg-gray-50'\n              }`}\n              animate={{ scale: index === step ? 1.02 : 1 }}\n            >\n              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${\n                index === step ? 'bg-blue-500 text-white' : 'bg-gray-300 text-gray-600'\n              }`}>\n                <Icon className=\"w-4 h-4\" />\n              </div>\n              <div>\n                <div className=\"font-medium text-sm text-gray-900\">{stepItem.text}</div>\n                <div className=\"text-xs text-gray-600\">{stepItem.detail}</div>\n              </div>\n            </motion.div>\n          )\n        })}\n      </div>\n\n      <div className=\"flex gap-2 mt-4\">\n        <Button\n          onClick={() => setStep(Math.max(0, step - 1))}\n          variant=\"outline\"\n          size=\"sm\"\n          disabled={step === 0}\n          className=\"flex-1\"\n        >\n          Previous\n        </Button>\n        <Button\n          onClick={() => setStep(Math.min(steps.length - 1, step + 1))}\n          size=\"sm\"\n          disabled={step === steps.length - 1}\n          className=\"flex-1\"\n        >\n          Next\n        </Button>\n      </div>\n    </div>\n  )\n}\n\n// PWA status indicator\nexport function PWAStatus() {\n  const [isOnline, setIsOnline] = useState(true)\n  const [isStandalone, setIsStandalone] = useState(false)\n  const [updateAvailable, setUpdateAvailable] = useState(false)\n\n  useEffect(() => {\n    setIsOnline(navigator.onLine)\n    setIsStandalone(window.matchMedia('(display-mode: standalone)').matches)\n\n    const handleOnline = () => setIsOnline(true)\n    const handleOffline = () => setIsOnline(false)\n\n    window.addEventListener('online', handleOnline)\n    window.addEventListener('offline', handleOffline)\n\n    // Check for service worker updates\n    if ('serviceWorker' in navigator) {\n      navigator.serviceWorker.addEventListener('controllerchange', () => {\n        setUpdateAvailable(true)\n      })\n    }\n\n    return () => {\n      window.removeEventListener('online', handleOnline)\n      window.removeEventListener('offline', handleOffline)\n    }\n  }, [])\n\n  const handleUpdate = () => {\n    window.location.reload()\n  }\n\n  if (!isStandalone) return null\n\n  return (\n    <div className=\"fixed top-0 left-0 right-0 z-50\">\n      <AnimatePresence>\n        {!isOnline && (\n          <motion.div\n            initial={{ y: -100 }}\n            animate={{ y: 0 }}\n            exit={{ y: -100 }}\n            className=\"bg-yellow-500 text-white text-center py-2 px-4 text-sm\"\n          >\n            You're offline. Some features may not work.\n          </motion.div>\n        )}\n        \n        {updateAvailable && (\n          <motion.div\n            initial={{ y: -100 }}\n            animate={{ y: 0 }}\n            exit={{ y: -100 }}\n            className=\"bg-blue-500 text-white text-center py-2 px-4 text-sm\"\n          >\n            <span>New version available! </span>\n            <button\n              onClick={handleUpdate}\n              className=\"underline font-medium hover:no-underline\"\n            >\n              Update now\n            </button>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  )\n}\n\n// Hook for PWA features\nexport function usePWA() {\n  const [isInstalled, setIsInstalled] = useState(false)\n  const [isStandalone, setIsStandalone] = useState(false)\n  const [isOnline, setIsOnline] = useState(true)\n  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null)\n\n  useEffect(() => {\n    setIsStandalone(window.matchMedia('(display-mode: standalone)').matches)\n    setIsOnline(navigator.onLine)\n\n    const handleBeforeInstallPrompt = (e: Event) => {\n      e.preventDefault()\n      setDeferredPrompt(e as BeforeInstallPromptEvent)\n    }\n\n    const handleAppInstalled = () => {\n      setIsInstalled(true)\n      setDeferredPrompt(null)\n    }\n\n    const handleOnline = () => setIsOnline(true)\n    const handleOffline = () => setIsOnline(false)\n\n    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)\n    window.addEventListener('appinstalled', handleAppInstalled)\n    window.addEventListener('online', handleOnline)\n    window.addEventListener('offline', handleOffline)\n\n    return () => {\n      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)\n      window.removeEventListener('appinstalled', handleAppInstalled)\n      window.removeEventListener('online', handleOnline)\n      window.removeEventListener('offline', handleOffline)\n    }\n  }, [])\n\n  const installApp = async () => {\n    if (!deferredPrompt) return false\n\n    deferredPrompt.prompt()\n    const { outcome } = await deferredPrompt.userChoice\n    \n    setDeferredPrompt(null)\n    return outcome === 'accepted'\n  }\n\n  return {\n    isInstalled,\n    isStandalone,\n    isOnline,\n    canInstall: !!deferredPrompt,\n    installApp\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;AALA;;;;;AAgBO,SAAS;;IACd,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmC;IACtF,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,oCAAoC;YACpC,gBAAgB,OAAO,UAAU,CAAC,8BAA8B,OAAO;YAEvE,eAAe;YACf,SAAS,mBAAmB,IAAI,CAAC,UAAU,SAAS;YAEpD,6BAA6B;YAC7B,IAAI,6BAA6B,WAAW;gBACzC,UAAkB,uBAAuB,GAAG,IAAI;8CAAC,CAAC;wBACjD,eAAe,YAAY,MAAM,GAAG;oBACtC;;YACF;YAEA,MAAM;oEAA4B,CAAC;oBACjC,EAAE,cAAc;oBAChB,kBAAkB;oBAElB,oCAAoC;oBACpC;4EAAW;4BACT,IAAI,CAAC,gBAAgB,CAAC,aAAa;gCACjC,qBAAqB;4BACvB;wBACF;2EAAG;gBACL;;YAEA,MAAM;6DAAqB;oBACzB,eAAe;oBACf,qBAAqB;oBACrB,kBAAkB;gBACpB;;YAEA,OAAO,gBAAgB,CAAC,uBAAuB;YAC/C,OAAO,gBAAgB,CAAC,gBAAgB;YAExC;0CAAO;oBACL,OAAO,mBAAmB,CAAC,uBAAuB;oBAClD,OAAO,mBAAmB,CAAC,gBAAgB;gBAC7C;;QACF;iCAAG;QAAC;QAAc;KAAY;IAE9B,MAAM,qBAAqB;QACzB,IAAI,CAAC,gBAAgB;QAErB,eAAe,MAAM;QACrB,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,eAAe,UAAU;QAEnD,IAAI,YAAY,YAAY;YAC1B,QAAQ,GAAG,CAAC;QACd,OAAO;YACL,QAAQ,GAAG,CAAC;QACd;QAEA,kBAAkB;QAClB,qBAAqB;IACvB;IAEA,MAAM,gBAAgB;QACpB,qBAAqB;QACrB,gCAAgC;QAChC,aAAa,OAAO,CAAC,yBAAyB,KAAK,GAAG,GAAG,QAAQ;IACnE;IAEA,wDAAwD;IACxD,IAAI,eAAe,cAAc,OAAO;IAExC,MAAM,gBAAgB,aAAa,OAAO,CAAC;IAC3C,IAAI,iBAAiB,KAAK,GAAG,KAAK,SAAS,iBAAiB,KAAK,KAAK,KAAK,MAAM;QAC/E,OAAO;IACT;IAEA,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,mCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAI;YAC9B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,MAAM;gBAAE,SAAS;gBAAG,GAAG;YAAI;YAC3B,WAAU;sBAEV,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,iNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;kDAExB,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA8B;;;;;;0DAC5C,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAGzC,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,SAAS;gCACT,WAAU;gCACV,YAAY;oCAAE,OAAO;gCAAI;gCACzB,UAAU;oCAAE,OAAO;gCAAI;0CAEvB,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIjB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;;;;;;;;;;kDAEjB,6LAAC;kDAAK;;;;;;;;;;;;0CAER,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;;;;;;;;;;kDAEjB,6LAAC;kDAAK;;;;;;;;;;;;0CAER,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;;;;;;;;;;kDAEjB,6LAAC;kDAAK;;;;;;;;;;;;;;;;;;oBAIT,sBACC,6LAAC;;;;6CAED,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,WAAU;gCACV,UAAU,CAAC;;kDAEX,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,SAAQ;gCACR,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GA3JgB;KAAA;AA6JhB,SAAS;;IACP,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,MAAM,QAAQ;QACZ;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,MAAM;YACN,QAAQ;QACV;QACA;YACE,MAAM,qMAAA,CAAA,OAAI;YACV,MAAM;YACN,QAAQ;QACV;QACA;YACE,MAAM,6MAAA,CAAA,WAAQ;YACd,MAAM;YACN,QAAQ;QACV;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAE,WAAU;0BAA6B;;;;;;0BAI1C,6LAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,UAAU;oBACpB,MAAM,OAAO,SAAS,IAAI;oBAC1B,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,WAAW,CAAC,yDAAyD,EACnE,UAAU,OAAO,sCAAsC,cACvD;wBACF,SAAS;4BAAE,OAAO,UAAU,OAAO,OAAO;wBAAE;;0CAE5C,6LAAC;gCAAI,WAAW,CAAC,sDAAsD,EACrE,UAAU,OAAO,2BAA2B,6BAC5C;0CACA,cAAA,6LAAC;oCAAK,WAAU;;;;;;;;;;;0CAElB,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;kDAAqC,SAAS,IAAI;;;;;;kDACjE,6LAAC;wCAAI,WAAU;kDAAyB,SAAS,MAAM;;;;;;;;;;;;;uBAbpD;;;;;gBAiBX;;;;;;0BAGF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS,IAAM,QAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;wBAC1C,SAAQ;wBACR,MAAK;wBACL,UAAU,SAAS;wBACnB,WAAU;kCACX;;;;;;kCAGD,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS,IAAM,QAAQ,KAAK,GAAG,CAAC,MAAM,MAAM,GAAG,GAAG,OAAO;wBACzD,MAAK;wBACL,UAAU,SAAS,MAAM,MAAM,GAAG;wBAClC,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAMT;IAzES;MAAA;AA4EF,SAAS;;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,YAAY,UAAU,MAAM;YAC5B,gBAAgB,OAAO,UAAU,CAAC,8BAA8B,OAAO;YAEvE,MAAM;oDAAe,IAAM,YAAY;;YACvC,MAAM;qDAAgB,IAAM,YAAY;;YAExC,OAAO,gBAAgB,CAAC,UAAU;YAClC,OAAO,gBAAgB,CAAC,WAAW;YAEnC,mCAAmC;YACnC,IAAI,mBAAmB,WAAW;gBAChC,UAAU,aAAa,CAAC,gBAAgB,CAAC;2CAAoB;wBAC3D,mBAAmB;oBACrB;;YACF;YAEA;uCAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;oBACrC,OAAO,mBAAmB,CAAC,WAAW;gBACxC;;QACF;8BAAG,EAAE;IAEL,MAAM,eAAe;QACnB,OAAO,QAAQ,CAAC,MAAM;IACxB;IAEA,IAAI,CAAC,cAAc,OAAO;IAE1B,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,4LAAA,CAAA,kBAAe;;gBACb,CAAC,0BACA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,GAAG,CAAC;oBAAI;oBACnB,SAAS;wBAAE,GAAG;oBAAE;oBAChB,MAAM;wBAAE,GAAG,CAAC;oBAAI;oBAChB,WAAU;8BACX;;;;;;gBAKF,iCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,GAAG,CAAC;oBAAI;oBACnB,SAAS;wBAAE,GAAG;oBAAE;oBAChB,MAAM;wBAAE,GAAG,CAAC;oBAAI;oBAChB,WAAU;;sCAEV,6LAAC;sCAAK;;;;;;sCACN,6LAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAQb;IAnEgB;MAAA;AAsET,SAAS;;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmC;IAEtF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,gBAAgB,OAAO,UAAU,CAAC,8BAA8B,OAAO;YACvE,YAAY,UAAU,MAAM;YAE5B,MAAM;8DAA4B,CAAC;oBACjC,EAAE,cAAc;oBAChB,kBAAkB;gBACpB;;YAEA,MAAM;uDAAqB;oBACzB,eAAe;oBACf,kBAAkB;gBACpB;;YAEA,MAAM;iDAAe,IAAM,YAAY;;YACvC,MAAM;kDAAgB,IAAM,YAAY;;YAExC,OAAO,gBAAgB,CAAC,uBAAuB;YAC/C,OAAO,gBAAgB,CAAC,gBAAgB;YACxC,OAAO,gBAAgB,CAAC,UAAU;YAClC,OAAO,gBAAgB,CAAC,WAAW;YAEnC;oCAAO;oBACL,OAAO,mBAAmB,CAAC,uBAAuB;oBAClD,OAAO,mBAAmB,CAAC,gBAAgB;oBAC3C,OAAO,mBAAmB,CAAC,UAAU;oBACrC,OAAO,mBAAmB,CAAC,WAAW;gBACxC;;QACF;2BAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI,CAAC,gBAAgB,OAAO;QAE5B,eAAe,MAAM;QACrB,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,eAAe,UAAU;QAEnD,kBAAkB;QAClB,OAAO,YAAY;IACrB;IAEA,OAAO;QACL;QACA;QACA;QACA,YAAY,CAAC,CAAC;QACd;IACF;AACF;IArDgB", "debugId": null}}]}