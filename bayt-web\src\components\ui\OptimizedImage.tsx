'use client'

import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ImageIcon, Loader2 } from 'lucide-react'

interface OptimizedImageProps {
  src: string
  alt: string
  className?: string
  width?: number
  height?: number
  priority?: boolean
  placeholder?: string
  onLoad?: () => void
  onError?: () => void
}

export function OptimizedImage({
  src,
  alt,
  className = '',
  width,
  height,
  priority = false,
  placeholder,
  onLoad,
  onError
}: OptimizedImageProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)
  const [isInView, setIsInView] = useState(priority)
  const imgRef = useRef<HTMLImageElement>(null)
  const observerRef = useRef<IntersectionObserver | null>(null)

  useEffect(() => {
    if (priority) return

    observerRef.current = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true)
          observerRef.current?.disconnect()
        }
      },
      { threshold: 0.1, rootMargin: '50px' }
    )

    if (imgRef.current) {
      observerRef.current.observe(imgRef.current)
    }

    return () => observerRef.current?.disconnect()
  }, [priority])

  const handleLoad = () => {
    setIsLoading(false)
    onLoad?.()
  }

  const handleError = () => {
    setIsLoading(false)
    setHasError(true)
    onError?.()
  }

  const getOptimizedSrc = (originalSrc: string, width?: number) => {
    // For external images, return as-is
    if (originalSrc.startsWith('http')) {
      return originalSrc
    }
    
    // For internal images, add optimization parameters
    const params = new URLSearchParams()
    if (width) params.set('w', width.toString())
    params.set('q', '80') // Quality
    params.set('f', 'webp') // Format
    
    return `${originalSrc}?${params.toString()}`
  }

  return (
    <div 
      ref={imgRef}
      className={`relative overflow-hidden ${className}`}
      style={{ width, height }}
    >
      <AnimatePresence mode="wait">
        {isLoading && (
          <motion.div
            key="loading"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 flex items-center justify-center bg-gray-100"
          >
            {placeholder ? (
              <img 
                src={placeholder} 
                alt="" 
                className="w-full h-full object-cover blur-sm scale-110"
              />
            ) : (
              <div className="flex flex-col items-center justify-center text-gray-400">
                <Loader2 className="w-8 h-8 animate-spin mb-2" />
                <span className="text-sm">Loading...</span>
              </div>
            )}
          </motion.div>
        )}

        {hasError && (
          <motion.div
            key="error"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="absolute inset-0 flex items-center justify-center bg-gray-100"
          >
            <div className="flex flex-col items-center justify-center text-gray-400">
              <ImageIcon className="w-8 h-8 mb-2" />
              <span className="text-sm">Failed to load</span>
            </div>
          </motion.div>
        )}

        {isInView && !hasError && (
          <motion.img
            key="image"
            src={getOptimizedSrc(src, width)}
            alt={alt}
            onLoad={handleLoad}
            onError={handleError}
            initial={{ opacity: 0, scale: 1.1 }}
            animate={{ 
              opacity: isLoading ? 0 : 1,
              scale: isLoading ? 1.1 : 1
            }}
            transition={{ 
              duration: 0.6,
              ease: "easeOut"
            }}
            className="w-full h-full object-cover"
            loading={priority ? "eager" : "lazy"}
            decoding="async"
          />
        )}
      </AnimatePresence>
    </div>
  )
}

// Hook for progressive image loading
export function useProgressiveImage(src: string, placeholder?: string) {
  const [currentSrc, setCurrentSrc] = useState(placeholder || '')
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const img = new Image()
    img.onload = () => {
      setCurrentSrc(src)
      setIsLoading(false)
    }
    img.src = src
  }, [src])

  return { currentSrc, isLoading }
}

// Component for progressive image loading
export function ProgressiveImage({
  src,
  placeholder,
  alt,
  className,
  ...props
}: OptimizedImageProps & { placeholder?: string }) {
  const { currentSrc, isLoading } = useProgressiveImage(src, placeholder)

  return (
    <motion.div className={`relative overflow-hidden ${className}`}>
      <motion.img
        src={currentSrc}
        alt={alt}
        className="w-full h-full object-cover"
        animate={{
          filter: isLoading ? 'blur(4px)' : 'blur(0px)',
          scale: isLoading ? 1.1 : 1
        }}
        transition={{ duration: 0.6 }}
        {...props}
      />
      
      <AnimatePresence>
        {isLoading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 flex items-center justify-center bg-white/80 backdrop-blur-sm"
          >
            <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  )
}

// Image gallery component with lazy loading
export function ImageGallery({ 
  images, 
  className = '' 
}: { 
  images: string[]
  className?: string 
}) {
  const [currentIndex, setCurrentIndex] = useState(0)

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="aspect-square rounded-2xl overflow-hidden">
        <OptimizedImage
          src={images[currentIndex]}
          alt={`Image ${currentIndex + 1}`}
          className="w-full h-full"
          priority={currentIndex === 0}
        />
      </div>
      
      {images.length > 1 && (
        <div className="flex gap-2 overflow-x-auto pb-2">
          {images.map((image, index) => (
            <motion.button
              key={index}
              onClick={() => setCurrentIndex(index)}
              className={`flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-colors ${
                index === currentIndex 
                  ? 'border-blue-500' 
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <OptimizedImage
                src={image}
                alt={`Thumbnail ${index + 1}`}
                className="w-full h-full"
              />
            </motion.button>
          ))}
        </div>
      )}
    </div>
  )
}
