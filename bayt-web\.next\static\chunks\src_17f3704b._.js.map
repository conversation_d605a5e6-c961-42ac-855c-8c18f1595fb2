{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Bayt/bayt-web/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Database Types\nexport interface User {\n  id: string\n  email: string\n  phone?: string\n  full_name: string\n  avatar_url?: string\n  location?: string\n  rating?: number\n  created_at: string\n  updated_at: string\n}\n\nexport interface Category {\n  id: string\n  name: string\n  name_ar: string\n  name_tr: string\n  icon: string\n  parent_id?: string\n  created_at: string\n}\n\nexport interface Product {\n  id: string\n  title: string\n  title_ar: string\n  title_tr: string\n  description: string\n  description_ar: string\n  description_tr: string\n  price: number\n  currency: string\n  condition: 'new' | 'like_new' | 'good' | 'fair' | 'poor'\n  category_id: string\n  user_id: string\n  images: string[]\n  location: string\n  latitude?: number\n  longitude?: number\n  is_active: boolean\n  is_sold: boolean\n  views_count: number\n  favorites_count: number\n  created_at: string\n  updated_at: string\n  category?: Category\n  user?: User\n}\n\nexport interface Message {\n  id: string\n  product_id: string\n  sender_id: string\n  receiver_id: string\n  content: string\n  is_read: boolean\n  created_at: string\n  product?: Product\n  sender?: User\n  receiver?: User\n}\n\nexport interface Favorite {\n  id: string\n  user_id: string\n  product_id: string\n  created_at: string\n  product?: Product\n}\n\nexport interface Review {\n  id: string\n  reviewer_id: string\n  reviewed_id: string\n  product_id?: string\n  rating: number\n  comment?: string\n  created_at: string\n  reviewer?: User\n  reviewed?: User\n  product?: Product\n}\n"], "names": [], "mappings": ";;;AAEoB;AAFpB;;AAEA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Bayt/bayt-web/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState } from 'react'\nimport { User as SupabaseUser, Session } from '@supabase/supabase-js'\nimport { supabase, User } from '@/lib/supabase'\nimport { toast } from 'react-hot-toast'\n\ninterface AuthContextType {\n  user: User | null\n  session: Session | null\n  loading: boolean\n  signUp: (email: string, password: string, fullName: string) => Promise<void>\n  signIn: (email: string, password: string) => Promise<void>\n  signOut: () => Promise<void>\n  updateProfile: (updates: Partial<User>) => Promise<void>\n  resetPassword: (email: string) => Promise<void>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [session, setSession] = useState<Session | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    // Get initial session\n    supabase.auth.getSession().then(({ data: { session } }) => {\n      setSession(session)\n      if (session?.user) {\n        fetchUserProfile(session.user.id)\n      } else {\n        setLoading(false)\n      }\n    })\n\n    // Listen for auth changes\n    const {\n      data: { subscription },\n    } = supabase.auth.onAuthStateChange(async (event, session) => {\n      setSession(session)\n      \n      if (session?.user) {\n        await fetchUserProfile(session.user.id)\n      } else {\n        setUser(null)\n        setLoading(false)\n      }\n    })\n\n    return () => subscription.unsubscribe()\n  }, [])\n\n  const fetchUserProfile = async (userId: string) => {\n    try {\n      const { data, error } = await supabase\n        .from('users')\n        .select('*')\n        .eq('id', userId)\n        .single()\n\n      if (error) throw error\n      setUser(data)\n    } catch (error) {\n      console.error('Error fetching user profile:', error)\n      toast.error('Failed to load user profile')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const signUp = async (email: string, password: string, fullName: string) => {\n    try {\n      setLoading(true)\n      const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n          data: {\n            full_name: fullName,\n          },\n        },\n      })\n\n      if (error) throw error\n\n      if (data.user && !data.session) {\n        toast.success('Please check your email to confirm your account')\n      } else {\n        toast.success('Account created successfully!')\n      }\n    } catch (error: any) {\n      toast.error(error.message || 'Failed to create account')\n      throw error\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const signIn = async (email: string, password: string) => {\n    try {\n      setLoading(true)\n      const { error } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      })\n\n      if (error) throw error\n      toast.success('Signed in successfully!')\n    } catch (error: any) {\n      toast.error(error.message || 'Failed to sign in')\n      throw error\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const signOut = async () => {\n    try {\n      setLoading(true)\n      const { error } = await supabase.auth.signOut()\n      if (error) throw error\n      toast.success('Signed out successfully!')\n    } catch (error: any) {\n      toast.error(error.message || 'Failed to sign out')\n      throw error\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const updateProfile = async (updates: Partial<User>) => {\n    if (!user) throw new Error('No user logged in')\n\n    try {\n      setLoading(true)\n      const { error } = await supabase\n        .from('users')\n        .update(updates)\n        .eq('id', user.id)\n\n      if (error) throw error\n\n      setUser({ ...user, ...updates })\n      toast.success('Profile updated successfully!')\n    } catch (error: any) {\n      toast.error(error.message || 'Failed to update profile')\n      throw error\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const resetPassword = async (email: string) => {\n    try {\n      const { error } = await supabase.auth.resetPasswordForEmail(email, {\n        redirectTo: `${window.location.origin}/reset-password`,\n      })\n\n      if (error) throw error\n      toast.success('Password reset email sent!')\n    } catch (error: any) {\n      toast.error(error.message || 'Failed to send reset email')\n      throw error\n    }\n  }\n\n  const value = {\n    user,\n    session,\n    loading,\n    signUp,\n    signIn,\n    signOut,\n    updateProfile,\n    resetPassword,\n  }\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AACA;;;AALA;;;;AAkBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,sBAAsB;YACtB,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI;0CAAC,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE;oBACpD,WAAW;oBACX,IAAI,SAAS,MAAM;wBACjB,iBAAiB,QAAQ,IAAI,CAAC,EAAE;oBAClC,OAAO;wBACL,WAAW;oBACb;gBACF;;YAEA,0BAA0B;YAC1B,MAAM,EACJ,MAAM,EAAE,YAAY,EAAE,EACvB,GAAG,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB;0CAAC,OAAO,OAAO;oBAChD,WAAW;oBAEX,IAAI,SAAS,MAAM;wBACjB,MAAM,iBAAiB,QAAQ,IAAI,CAAC,EAAE;oBACxC,OAAO;wBACL,QAAQ;wBACR,WAAW;oBACb;gBACF;;YAEA;0CAAO,IAAM,aAAa,WAAW;;QACvC;iCAAG,EAAE;IAEL,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;YAET,IAAI,OAAO,MAAM;YACjB,QAAQ;QACV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,SAAS,OAAO,OAAe,UAAkB;QACrD,IAAI;YACF,WAAW;YACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;gBACjD;gBACA;gBACA,SAAS;oBACP,MAAM;wBACJ,WAAW;oBACb;gBACF;YACF;YAEA,IAAI,OAAO,MAAM;YAEjB,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,OAAO,EAAE;gBAC9B,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;QACF,EAAE,OAAO,OAAY;YACnB,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC7B,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,IAAI;YACF,WAAW;YACX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;gBACvD;gBACA;YACF;YAEA,IAAI,OAAO,MAAM;YACjB,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAY;YACnB,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC7B,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,UAAU;QACd,IAAI;YACF,WAAW;YACX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;YAC7C,IAAI,OAAO,MAAM;YACjB,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAY;YACnB,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC7B,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;QAE3B,IAAI;YACF,WAAW;YACX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,SACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,KAAK,EAAE;YAEnB,IAAI,OAAO,MAAM;YAEjB,QAAQ;gBAAE,GAAG,IAAI;gBAAE,GAAG,OAAO;YAAC;YAC9B,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAY;YACnB,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC7B,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO;gBACjE,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,eAAe,CAAC;YACxD;YAEA,IAAI,OAAO,MAAM;YACjB,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAY;YACnB,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC7B,MAAM;QACR;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBAAO,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;GA/JgB;KAAA;AAiKT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}]}