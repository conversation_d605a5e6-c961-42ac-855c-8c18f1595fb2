'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { Heart, MapPin, Eye, Star, Clock, TrendingUp, Sparkles } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { supabase, Product } from '@/lib/supabase'
import { formatPrice, formatRelativeTime, getImageUrl } from '@/lib/utils'

export function FeaturedProducts() {
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchFeaturedProducts()
  }, [])

  const fetchFeaturedProducts = async () => {
    try {
      const { data, error } = await supabase
        .from('products')
        .select(`
          *,
          category:categories(*),
          user:users(*)
        `)
        .eq('is_active', true)
        .eq('is_sold', false)
        .order('created_at', { ascending: false })
        .limit(8)

      if (error) throw error
      setProducts(data || [])
    } catch (error) {
      console.error('Error fetching featured products:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900">Latest Listings</h2>
            <p className="mt-4 text-lg text-gray-600">
              Discover the newest items added to our marketplace
            </p>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {Array.from({ length: 8 }).map((_, index) => (
              <div key={index} className="animate-pulse">
                <div className="bg-white rounded-lg shadow-sm overflow-hidden">
                  <div className="bg-gray-200 h-48"></div>
                  <div className="p-4 space-y-3">
                    <div className="bg-gray-200 h-4 rounded"></div>
                    <div className="bg-gray-200 h-4 rounded w-3/4"></div>
                    <div className="bg-gray-200 h-4 rounded w-1/2"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    )
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  }

  return (
    <section className="py-16 bg-gradient-to-br from-gray-50 to-blue-50/30">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <motion.div
            className="inline-flex items-center gap-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium mb-4"
            whileHover={{ scale: 1.05 }}
          >
            <TrendingUp className="w-4 h-4" />
            Hot Deals
          </motion.div>
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Latest Listings</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Discover the newest items added to our marketplace with verified sellers and great prices
          </p>
        </motion.div>

        <motion.div
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {products.map((product, index) => (
            <motion.div key={product.id} variants={itemVariants}>
              <ProductCard product={product} index={index} />
            </motion.div>
          ))}
        </motion.div>

        <motion.div
          className="text-center mt-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Link
              href="/products"
              className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl text-base font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              <TrendingUp className="w-5 h-5 mr-2" />
              View All Products
            </Link>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}

function ProductCard({ product, index }: { product: Product; index: number }) {
  const [isFavorited, setIsFavorited] = useState(false)
  const [isHovered, setIsHovered] = useState(false)

  const handleFavoriteClick = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsFavorited(!isFavorited)
    // TODO: Implement favorite functionality
  }

  const mainImage = product.images && product.images.length > 0
    ? getImageUrl(product.images[0])
    : '/placeholder-image.jpg'

  return (
    <motion.div
      whileHover={{ y: -8, scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      className="group"
    >
      <Link href={`/product/${product.id}`}>
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-500 border border-white/20">
          <div className="relative overflow-hidden">
            <motion.img
              src={mainImage}
              alt={product.title}
              className="w-full h-48 object-cover"
              animate={{ scale: isHovered ? 1.1 : 1 }}
              transition={{ duration: 0.6, ease: "easeOut" }}
            />

            {/* Gradient Overlay */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"
              animate={{ opacity: isHovered ? 1 : 0 }}
              transition={{ duration: 0.3 }}
            />

            <motion.button
              onClick={handleFavoriteClick}
              className="absolute top-3 right-3 p-2 bg-white/90 backdrop-blur-sm rounded-full shadow-lg hover:shadow-xl transition-all duration-300"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              animate={{
                backgroundColor: isFavorited ? "rgba(239, 68, 68, 0.9)" : "rgba(255, 255, 255, 0.9)"
              }}
            >
              <Heart
                className={`w-4 h-4 transition-colors ${
                  isFavorited ? 'text-white fill-current' : 'text-gray-600'
                }`}
              />
            </motion.button>

            {product.is_featured && (
              <motion.div
                className="absolute top-3 left-3 bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg"
                animate={{
                  scale: [1, 1.1, 1],
                  boxShadow: [
                    "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
                    "0 10px 15px -3px rgba(0, 0, 0, 0.1)",
                    "0 4px 6px -1px rgba(0, 0, 0, 0.1)"
                  ]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              >
                <Sparkles className="w-3 h-3 inline mr-1" />
                Featured
              </motion.div>
            )}

            {/* Quick View Button */}
            <AnimatePresence>
              {isHovered && (
                <motion.div
                  className="absolute bottom-3 left-3 right-3"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 20 }}
                  transition={{ duration: 0.3 }}
                >
                  <button className="w-full bg-white/90 backdrop-blur-sm text-gray-900 py-2 rounded-lg font-medium hover:bg-white transition-colors">
                    Quick View
                  </button>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

        <div className="p-4">
          <h3 className="font-medium text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-2">
            {product.title}
          </h3>
          
          <div className="mt-2 flex items-center justify-between">
            <span className="text-lg font-bold text-blue-600">
              {formatPrice(product.price, product.currency)}
            </span>
            <span className="text-sm text-gray-500 capitalize">
              {product.condition.replace('_', ' ')}
            </span>
          </div>

          <div className="mt-3 flex items-center text-sm text-gray-500">
            <MapPin className="w-4 h-4 mr-1" />
            <span className="truncate">{product.location}</span>
          </div>

          <div className="mt-3 flex items-center justify-between text-sm text-gray-500">
            <span>{formatRelativeTime(product.created_at)}</span>
            <div className="flex items-center space-x-3">
              <div className="flex items-center">
                <Eye className="w-4 h-4 mr-1" />
                <span>{product.views_count}</span>
              </div>
              <div className="flex items-center">
                <Heart className="w-4 h-4 mr-1" />
                <span>{product.favorites_count}</span>
              </div>
            </div>
          </div>

          {product.user && (
            <div className="mt-3 pt-3 border-t border-gray-100 flex items-center">
              <div className="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center mr-2">
                {product.user.avatar_url ? (
                  <img
                    src={product.user.avatar_url}
                    alt={product.user.full_name}
                    className="w-6 h-6 rounded-full object-cover"
                  />
                ) : (
                  <span className="text-xs text-gray-600">
                    {product.user.full_name.charAt(0)}
                  </span>
                )}
              </div>
              <span className="text-sm text-gray-600 truncate">
                {product.user.full_name}
              </span>
              {product.user.rating && (
                <div className="ml-auto flex items-center">
                  <span className="text-xs text-yellow-500">★</span>
                  <span className="text-xs text-gray-600 ml-1">
                    {product.user.rating.toFixed(1)}
                  </span>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </Link>
  </motion.div>
  )
}
