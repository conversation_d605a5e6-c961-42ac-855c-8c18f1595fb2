'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  TrendingUp, 
  TrendingDown, 
  Eye, 
  Heart, 
  MessageCircle, 
  DollarSign,
  Users,
  ShoppingCart,
  Calendar,
  MapPin,
  BarChart3,
  PieChart,
  Activity
} from 'lucide-react'
import { useThemeClasses } from '@/components/theme/ThemeProvider'

interface AnalyticsData {
  views: { value: number; change: number; trend: 'up' | 'down' }
  favorites: { value: number; change: number; trend: 'up' | 'down' }
  messages: { value: number; change: number; trend: 'up' | 'down' }
  sales: { value: number; change: number; trend: 'up' | 'down' }
  revenue: { value: number; change: number; trend: 'up' | 'down' }
  conversionRate: { value: number; change: number; trend: 'up' | 'down' }
}

interface ChartData {
  labels: string[]
  datasets: {
    label: string
    data: number[]
    color: string
  }[]
}

export function AnalyticsDashboard() {
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d')
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null)
  const [chartData, setChartData] = useState<ChartData | null>(null)
  const [loading, setLoading] = useState(true)
  const themeClasses = useThemeClasses()

  useEffect(() => {
    fetchAnalytics()
  }, [timeRange])

  const fetchAnalytics = async () => {
    setLoading(true)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Mock data
    const mockAnalytics: AnalyticsData = {
      views: { value: 12543, change: 15.3, trend: 'up' },
      favorites: { value: 1234, change: -2.1, trend: 'down' },
      messages: { value: 456, change: 8.7, trend: 'up' },
      sales: { value: 89, change: 23.4, trend: 'up' },
      revenue: { value: 15678, change: 18.9, trend: 'up' },
      conversionRate: { value: 3.2, change: 0.5, trend: 'up' }
    }

    const mockChartData: ChartData = {
      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
      datasets: [
        {
          label: 'Views',
          data: [1200, 1900, 3000, 5000, 2000, 3000],
          color: '#3B82F6'
        },
        {
          label: 'Sales',
          data: [12, 19, 30, 50, 20, 30],
          color: '#10B981'
        }
      ]
    }

    setAnalytics(mockAnalytics)
    setChartData(mockChartData)
    setLoading(false)
  }

  const StatCard = ({ 
    title, 
    value, 
    change, 
    trend, 
    icon: Icon, 
    format = 'number' 
  }: {
    title: string
    value: number
    change: number
    trend: 'up' | 'down'
    icon: React.ComponentType<any>
    format?: 'number' | 'currency' | 'percentage'
  }) => {
    const formatValue = (val: number) => {
      switch (format) {
        case 'currency':
          return `$${val.toLocaleString()}`
        case 'percentage':
          return `${val}%`
        default:
          return val.toLocaleString()
      }
    }

    return (
      <motion.div
        className={`p-6 rounded-2xl ${themeClasses.surface} ${themeClasses.border} border`}
        whileHover={{ scale: 1.02 }}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <div className="flex items-center justify-between mb-4">
          <div className={`p-3 rounded-xl ${themeClasses.primaryBg}`}>
            <Icon className={`w-6 h-6 ${themeClasses.primaryText}`} />
          </div>
          <div className={`flex items-center gap-1 text-sm ${
            trend === 'up' ? 'text-green-600' : 'text-red-600'
          }`}>
            {trend === 'up' ? (
              <TrendingUp className="w-4 h-4" />
            ) : (
              <TrendingDown className="w-4 h-4" />
            )}
            {Math.abs(change)}%
          </div>
        </div>
        
        <div className={`text-2xl font-bold ${themeClasses.text} mb-1`}>
          {formatValue(value)}
        </div>
        
        <div className={`text-sm ${themeClasses.textSecondary}`}>
          {title}
        </div>
      </motion.div>
    )
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="h-8 bg-gray-200 rounded w-48 animate-pulse" />
          <div className="h-10 bg-gray-200 rounded w-32 animate-pulse" />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i} className="h-32 bg-gray-200 rounded-2xl animate-pulse" />
          ))}
        </div>
        
        <div className="h-80 bg-gray-200 rounded-2xl animate-pulse" />
      </div>
    )
  }

  if (!analytics || !chartData) return null

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className={`text-3xl font-bold ${themeClasses.text}`}>Analytics</h1>
          <p className={`${themeClasses.textSecondary} mt-1`}>
            Track your marketplace performance
          </p>
        </div>
        
        <div className="flex gap-2">
          {(['7d', '30d', '90d', '1y'] as const).map((range) => (
            <motion.button
              key={range}
              onClick={() => setTimeRange(range)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                timeRange === range
                  ? `${themeClasses.primary} text-white`
                  : `${themeClasses.surface} ${themeClasses.text} hover:${themeClasses.primaryBg}`
              }`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {range}
            </motion.button>
          ))}
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <StatCard
          title="Total Views"
          value={analytics.views.value}
          change={analytics.views.change}
          trend={analytics.views.trend}
          icon={Eye}
        />
        <StatCard
          title="Favorites"
          value={analytics.favorites.value}
          change={analytics.favorites.change}
          trend={analytics.favorites.trend}
          icon={Heart}
        />
        <StatCard
          title="Messages"
          value={analytics.messages.value}
          change={analytics.messages.change}
          trend={analytics.messages.trend}
          icon={MessageCircle}
        />
        <StatCard
          title="Sales"
          value={analytics.sales.value}
          change={analytics.sales.change}
          trend={analytics.sales.trend}
          icon={ShoppingCart}
        />
        <StatCard
          title="Revenue"
          value={analytics.revenue.value}
          change={analytics.revenue.change}
          trend={analytics.revenue.trend}
          icon={DollarSign}
          format="currency"
        />
        <StatCard
          title="Conversion Rate"
          value={analytics.conversionRate.value}
          change={analytics.conversionRate.change}
          trend={analytics.conversionRate.trend}
          icon={TrendingUp}
          format="percentage"
        />
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Line Chart */}
        <motion.div
          className={`p-6 rounded-2xl ${themeClasses.surface} ${themeClasses.border} border`}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <div className="flex items-center justify-between mb-6">
            <h3 className={`text-lg font-semibold ${themeClasses.text}`}>
              Performance Trends
            </h3>
            <BarChart3 className={`w-5 h-5 ${themeClasses.textSecondary}`} />
          </div>
          
          <div className="h-64 flex items-end justify-between gap-2">
            {chartData.labels.map((label, index) => {
              const maxValue = Math.max(...chartData.datasets[0].data)
              const height = (chartData.datasets[0].data[index] / maxValue) * 100
              
              return (
                <div key={label} className="flex-1 flex flex-col items-center">
                  <motion.div
                    className={`w-full ${themeClasses.primary} rounded-t-lg mb-2`}
                    style={{ height: `${height}%` }}
                    initial={{ height: 0 }}
                    animate={{ height: `${height}%` }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                  />
                  <span className={`text-xs ${themeClasses.textSecondary}`}>
                    {label}
                  </span>
                </div>
              )
            })}
          </div>
        </motion.div>

        {/* Pie Chart */}
        <motion.div
          className={`p-6 rounded-2xl ${themeClasses.surface} ${themeClasses.border} border`}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <div className="flex items-center justify-between mb-6">
            <h3 className={`text-lg font-semibold ${themeClasses.text}`}>
              Traffic Sources
            </h3>
            <PieChart className={`w-5 h-5 ${themeClasses.textSecondary}`} />
          </div>
          
          <div className="space-y-4">
            {[
              { label: 'Direct', value: 45, color: 'bg-blue-500' },
              { label: 'Search', value: 30, color: 'bg-green-500' },
              { label: 'Social', value: 15, color: 'bg-yellow-500' },
              { label: 'Referral', value: 10, color: 'bg-purple-500' }
            ].map((item, index) => (
              <motion.div
                key={item.label}
                className="flex items-center gap-3"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: 0.4 + index * 0.1 }}
              >
                <div className={`w-3 h-3 rounded-full ${item.color}`} />
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <span className={`text-sm ${themeClasses.text}`}>
                      {item.label}
                    </span>
                    <span className={`text-sm font-medium ${themeClasses.text}`}>
                      {item.value}%
                    </span>
                  </div>
                  <div className={`w-full h-2 ${themeClasses.surface} rounded-full mt-1`}>
                    <motion.div
                      className={`h-full ${item.color} rounded-full`}
                      initial={{ width: 0 }}
                      animate={{ width: `${item.value}%` }}
                      transition={{ duration: 0.5, delay: 0.5 + index * 0.1 }}
                    />
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>

      {/* Recent Activity */}
      <motion.div
        className={`p-6 rounded-2xl ${themeClasses.surface} ${themeClasses.border} border`}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.4 }}
      >
        <div className="flex items-center justify-between mb-6">
          <h3 className={`text-lg font-semibold ${themeClasses.text}`}>
            Recent Activity
          </h3>
          <Activity className={`w-5 h-5 ${themeClasses.textSecondary}`} />
        </div>
        
        <div className="space-y-4">
          {[
            { action: 'New product view', item: 'iPhone 15 Pro Max', time: '2 minutes ago', icon: Eye },
            { action: 'Product favorited', item: 'BMW X5 2020', time: '5 minutes ago', icon: Heart },
            { action: 'Message received', item: 'About Modern Apartment', time: '10 minutes ago', icon: MessageCircle },
            { action: 'Product sold', item: 'Gaming Laptop', time: '1 hour ago', icon: ShoppingCart }
          ].map((activity, index) => {
            const Icon = activity.icon
            return (
              <motion.div
                key={index}
                className="flex items-center gap-4 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: 0.5 + index * 0.1 }}
              >
                <div className={`p-2 rounded-lg ${themeClasses.primaryBg}`}>
                  <Icon className={`w-4 h-4 ${themeClasses.primaryText}`} />
                </div>
                <div className="flex-1">
                  <div className={`font-medium ${themeClasses.text}`}>
                    {activity.action}
                  </div>
                  <div className={`text-sm ${themeClasses.textSecondary}`}>
                    {activity.item}
                  </div>
                </div>
                <div className={`text-sm ${themeClasses.textSecondary}`}>
                  {activity.time}
                </div>
              </motion.div>
            )
          })}
        </div>
      </motion.div>
    </div>
  )
}
