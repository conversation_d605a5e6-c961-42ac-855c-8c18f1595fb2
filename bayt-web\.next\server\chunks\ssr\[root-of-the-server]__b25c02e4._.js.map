{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Bayt/bayt-web/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPrice(price: number, currency: string = 'USD') {\n  const formatters = {\n    USD: new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }),\n    EUR: new Intl.NumberFormat('en-EU', { style: 'currency', currency: 'EUR' }),\n    TRY: new Intl.NumberFormat('tr-TR', { style: 'currency', currency: 'TRY' }),\n    SYP: new Intl.NumberFormat('ar-SY', { style: 'currency', currency: 'SYP' }),\n  }\n  \n  const formatter = formatters[currency as keyof typeof formatters] || formatters.USD\n  return formatter.format(price)\n}\n\nexport function formatDate(date: string | Date, locale: string = 'en-US') {\n  const dateObj = typeof date === 'string' ? new Date(date) : date\n  return new Intl.DateTimeFormat(locale, {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  }).format(dateObj)\n}\n\nexport function formatRelativeTime(date: string | Date, locale: string = 'en-US') {\n  const dateObj = typeof date === 'string' ? new Date(date) : date\n  const now = new Date()\n  const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000)\n  \n  if (diffInSeconds < 60) return 'Just now'\n  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`\n  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`\n  if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)} days ago`\n  \n  return formatDate(dateObj, locale)\n}\n\nexport function slugify(text: string) {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '')\n    .replace(/[\\s_-]+/g, '-')\n    .replace(/^-+|-+$/g, '')\n}\n\nexport function truncateText(text: string, maxLength: number) {\n  if (text.length <= maxLength) return text\n  return text.slice(0, maxLength) + '...'\n}\n\nexport function getImageUrl(path: string | null, bucket: string = 'products') {\n  if (!path) return '/placeholder-image.jpg'\n  \n  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL\n  return `${supabaseUrl}/storage/v1/object/public/${bucket}/${path}`\n}\n\nexport function validateFileType(file: File, allowedTypes: string[]) {\n  return allowedTypes.includes(file.type)\n}\n\nexport function validateFileSize(file: File, maxSizeInBytes: number) {\n  return file.size <= maxSizeInBytes\n}\n\nexport function generateUniqueFileName(originalName: string) {\n  const timestamp = Date.now()\n  const random = Math.random().toString(36).substring(2)\n  const extension = originalName.split('.').pop()\n  return `${timestamp}-${random}.${extension}`\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa,EAAE,WAAmB,KAAK;IACjE,MAAM,aAAa;QACjB,KAAK,IAAI,KAAK,YAAY,CAAC,SAAS;YAAE,OAAO;YAAY,UAAU;QAAM;QACzE,KAAK,IAAI,KAAK,YAAY,CAAC,SAAS;YAAE,OAAO;YAAY,UAAU;QAAM;QACzE,KAAK,IAAI,KAAK,YAAY,CAAC,SAAS;YAAE,OAAO;YAAY,UAAU;QAAM;QACzE,KAAK,IAAI,KAAK,YAAY,CAAC,SAAS;YAAE,OAAO;YAAY,UAAU;QAAM;IAC3E;IAEA,MAAM,YAAY,UAAU,CAAC,SAAoC,IAAI,WAAW,GAAG;IACnF,OAAO,UAAU,MAAM,CAAC;AAC1B;AAEO,SAAS,WAAW,IAAmB,EAAE,SAAiB,OAAO;IACtE,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,IAAI,KAAK,cAAc,CAAC,QAAQ;QACrC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,mBAAmB,IAAmB,EAAE,SAAiB,OAAO;IAC9E,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,QAAQ,OAAO,EAAE,IAAI;IAEvE,IAAI,gBAAgB,IAAI,OAAO;IAC/B,IAAI,gBAAgB,MAAM,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,IAAI,YAAY,CAAC;IAChF,IAAI,gBAAgB,OAAO,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,MAAM,UAAU,CAAC;IACjF,IAAI,gBAAgB,QAAQ,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,OAAO,SAAS,CAAC;IAElF,OAAO,WAAW,SAAS;AAC7B;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,aAAa,IACrB,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,YAAY;AACzB;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,SAAS,YAAY,IAAmB,EAAE,SAAiB,UAAU;IAC1E,IAAI,CAAC,MAAM,OAAO;IAElB,MAAM;IACN,OAAO,GAAG,YAAY,0BAA0B,EAAE,OAAO,CAAC,EAAE,MAAM;AACpE;AAEO,SAAS,iBAAiB,IAAU,EAAE,YAAsB;IACjE,OAAO,aAAa,QAAQ,CAAC,KAAK,IAAI;AACxC;AAEO,SAAS,iBAAiB,IAAU,EAAE,cAAsB;IACjE,OAAO,KAAK,IAAI,IAAI;AACtB;AAEO,SAAS,uBAAuB,YAAoB;IACzD,MAAM,YAAY,KAAK,GAAG;IAC1B,MAAM,SAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC;IACpD,MAAM,YAAY,aAAa,KAAK,CAAC,KAAK,GAAG;IAC7C,OAAO,GAAG,UAAU,CAAC,EAAE,OAAO,CAAC,EAAE,WAAW;AAC9C", "debugId": null}}, {"offset": {"line": 120, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Bayt/bayt-web/src/components/ui/Button.tsx"], "sourcesContent": ["import { forwardRef } from 'react'\nimport { cva, type VariantProps } from 'class-variance-authority'\nimport { cn } from '@/lib/utils'\nimport { Loader2 } from 'lucide-react'\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background',\n  {\n    variants: {\n      variant: {\n        default: 'bg-blue-600 text-white hover:bg-blue-700',\n        destructive: 'bg-red-600 text-white hover:bg-red-700',\n        outline: 'border border-gray-300 bg-white hover:bg-gray-50 text-gray-900',\n        secondary: 'bg-gray-100 text-gray-900 hover:bg-gray-200',\n        ghost: 'hover:bg-gray-100 text-gray-900',\n        link: 'underline-offset-4 hover:underline text-blue-600',\n      },\n      size: {\n        default: 'h-10 py-2 px-4',\n        sm: 'h-9 px-3 rounded-md',\n        lg: 'h-11 px-8 rounded-md',\n        icon: 'h-10 w-10',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  loading?: boolean\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, loading, children, disabled, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n        {children}\n      </button>\n    )\n  }\n)\nButton.displayName = 'Button'\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,wQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACtB,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACpE,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBAAW,8OAAC,iNAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YAC9B;;;;;;;AAGP;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 190, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Bayt/bayt-web/src/components/ui/Input.tsx"], "sourcesContent": ["import { forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {\n  error?: string\n}\n\nconst Input = forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, error, ...props }, ref) => {\n    return (\n      <div className=\"w-full\">\n        <input\n          type={type}\n          className={cn(\n            'flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',\n            error && 'border-red-500 focus-visible:ring-red-500',\n            className\n          )}\n          ref={ref}\n          {...props}\n        />\n        {error && (\n          <p className=\"mt-1 text-sm text-red-600\">{error}</p>\n        )}\n      </div>\n    )\n  }\n)\nInput.displayName = 'Input'\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACrB,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE;IACrC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,MAAM;gBACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qVACA,SAAS,6CACT;gBAEF,KAAK;gBACJ,GAAG,KAAK;;;;;;YAEV,uBACC,8OAAC;gBAAE,WAAU;0BAA6B;;;;;;;;;;;;AAIlD;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 236, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Bayt/bayt-web/src/components/auth/AuthModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useForm } from 'react-hook-form'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport { z } from 'zod'\nimport { X, Eye, EyeOff, Mail, Lock, User } from 'lucide-react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { Button } from '@/components/ui/Button'\nimport { Input } from '@/components/ui/Input'\n\nconst signInSchema = z.object({\n  email: z.string().email('Invalid email address'),\n  password: z.string().min(6, 'Password must be at least 6 characters'),\n})\n\nconst signUpSchema = z.object({\n  email: z.string().email('Invalid email address'),\n  password: z.string().min(6, 'Password must be at least 6 characters'),\n  fullName: z.string().min(2, 'Full name must be at least 2 characters'),\n  confirmPassword: z.string(),\n}).refine((data) => data.password === data.confirmPassword, {\n  message: \"Passwords don't match\",\n  path: [\"confirmPassword\"],\n})\n\ntype SignInForm = z.infer<typeof signInSchema>\ntype SignUpForm = z.infer<typeof signUpSchema>\n\ninterface AuthModalProps {\n  isOpen: boolean\n  onClose: () => void\n  defaultTab?: 'signin' | 'signup'\n}\n\nexport function AuthModal({ isOpen, onClose, defaultTab = 'signin' }: AuthModalProps) {\n  const [activeTab, setActiveTab] = useState(defaultTab)\n  const [showPassword, setShowPassword] = useState(false)\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false)\n  const { signIn, signUp, loading } = useAuth()\n\n  const signInForm = useForm<SignInForm>({\n    resolver: zodResolver(signInSchema),\n  })\n\n  const signUpForm = useForm<SignUpForm>({\n    resolver: zodResolver(signUpSchema),\n  })\n\n  const handleSignIn = async (data: SignInForm) => {\n    try {\n      await signIn(data.email, data.password)\n      onClose()\n    } catch (error) {\n      // Error is handled in the auth context\n    }\n  }\n\n  const handleSignUp = async (data: SignUpForm) => {\n    try {\n      await signUp(data.email, data.password, data.fullName)\n      onClose()\n    } catch (error) {\n      // Error is handled in the auth context\n    }\n  }\n\n  if (!isOpen) return null\n\n  return (\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50\">\n      <div className=\"bg-white rounded-lg shadow-xl w-full max-w-md mx-4\">\n        <div className=\"flex items-center justify-between p-6 border-b\">\n          <h2 className=\"text-xl font-semibold\">\n            {activeTab === 'signin' ? 'Sign In' : 'Sign Up'}\n          </h2>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600\"\n          >\n            <X className=\"w-6 h-6\" />\n          </button>\n        </div>\n\n        <div className=\"p-6\">\n          {/* Tab Buttons */}\n          <div className=\"flex mb-6 bg-gray-100 rounded-lg p-1\">\n            <button\n              onClick={() => setActiveTab('signin')}\n              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n                activeTab === 'signin'\n                  ? 'bg-white text-blue-600 shadow-sm'\n                  : 'text-gray-600 hover:text-gray-900'\n              }`}\n            >\n              Sign In\n            </button>\n            <button\n              onClick={() => setActiveTab('signup')}\n              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n                activeTab === 'signup'\n                  ? 'bg-white text-blue-600 shadow-sm'\n                  : 'text-gray-600 hover:text-gray-900'\n              }`}\n            >\n              Sign Up\n            </button>\n          </div>\n\n          {/* Sign In Form */}\n          {activeTab === 'signin' && (\n            <form onSubmit={signInForm.handleSubmit(handleSignIn)} className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Email\n                </label>\n                <div className=\"relative\">\n                  <Mail className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n                  <Input\n                    {...signInForm.register('email')}\n                    type=\"email\"\n                    placeholder=\"Enter your email\"\n                    className=\"pl-10\"\n                    error={signInForm.formState.errors.email?.message}\n                  />\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Password\n                </label>\n                <div className=\"relative\">\n                  <Lock className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n                  <Input\n                    {...signInForm.register('password')}\n                    type={showPassword ? 'text' : 'password'}\n                    placeholder=\"Enter your password\"\n                    className=\"pl-10 pr-10\"\n                    error={signInForm.formState.errors.password?.message}\n                  />\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowPassword(!showPassword)}\n                    className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\"\n                  >\n                    {showPassword ? <EyeOff className=\"w-5 h-5\" /> : <Eye className=\"w-5 h-5\" />}\n                  </button>\n                </div>\n              </div>\n\n              <Button\n                type=\"submit\"\n                className=\"w-full\"\n                loading={loading}\n              >\n                Sign In\n              </Button>\n            </form>\n          )}\n\n          {/* Sign Up Form */}\n          {activeTab === 'signup' && (\n            <form onSubmit={signUpForm.handleSubmit(handleSignUp)} className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Full Name\n                </label>\n                <div className=\"relative\">\n                  <User className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n                  <Input\n                    {...signUpForm.register('fullName')}\n                    type=\"text\"\n                    placeholder=\"Enter your full name\"\n                    className=\"pl-10\"\n                    error={signUpForm.formState.errors.fullName?.message}\n                  />\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Email\n                </label>\n                <div className=\"relative\">\n                  <Mail className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n                  <Input\n                    {...signUpForm.register('email')}\n                    type=\"email\"\n                    placeholder=\"Enter your email\"\n                    className=\"pl-10\"\n                    error={signUpForm.formState.errors.email?.message}\n                  />\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Password\n                </label>\n                <div className=\"relative\">\n                  <Lock className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n                  <Input\n                    {...signUpForm.register('password')}\n                    type={showPassword ? 'text' : 'password'}\n                    placeholder=\"Enter your password\"\n                    className=\"pl-10 pr-10\"\n                    error={signUpForm.formState.errors.password?.message}\n                  />\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowPassword(!showPassword)}\n                    className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\"\n                  >\n                    {showPassword ? <EyeOff className=\"w-5 h-5\" /> : <Eye className=\"w-5 h-5\" />}\n                  </button>\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Confirm Password\n                </label>\n                <div className=\"relative\">\n                  <Lock className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n                  <Input\n                    {...signUpForm.register('confirmPassword')}\n                    type={showConfirmPassword ? 'text' : 'password'}\n                    placeholder=\"Confirm your password\"\n                    className=\"pl-10 pr-10\"\n                    error={signUpForm.formState.errors.confirmPassword?.message}\n                  />\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n                    className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\"\n                  >\n                    {showConfirmPassword ? <EyeOff className=\"w-5 h-5\" /> : <Eye className=\"w-5 h-5\" />}\n                  </button>\n                </div>\n              </div>\n\n              <Button\n                type=\"submit\"\n                className=\"w-full\"\n                loading={loading}\n              >\n                Sign Up\n              </Button>\n            </form>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AATA;;;;;;;;;;AAWA,MAAM,eAAe,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC5B,OAAO,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC9B;AAEA,MAAM,eAAe,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC5B,OAAO,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,UAAU,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,iBAAiB,iLAAA,CAAA,IAAC,CAAC,MAAM;AAC3B,GAAG,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,KAAK,KAAK,eAAe,EAAE;IAC1D,SAAS;IACT,MAAM;QAAC;KAAkB;AAC3B;AAWO,SAAS,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,aAAa,QAAQ,EAAkB;IAClF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAE1C,MAAM,aAAa,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAc;QACrC,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,aAAa,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAc;QACrC,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,OAAO,KAAK,KAAK,EAAE,KAAK,QAAQ;YACtC;QACF,EAAE,OAAO,OAAO;QACd,uCAAuC;QACzC;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,OAAO,KAAK,KAAK,EAAE,KAAK,QAAQ,EAAE,KAAK,QAAQ;YACrD;QACF,EAAE,OAAO,OAAO;QACd,uCAAuC;QACzC;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX,cAAc,WAAW,YAAY;;;;;;sCAExC,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAIjB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,aAAa;oCAC5B,WAAW,CAAC,kEAAkE,EAC5E,cAAc,WACV,qCACA,qCACJ;8CACH;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,aAAa;oCAC5B,WAAW,CAAC,kEAAkE,EAC5E,cAAc,WACV,qCACA,qCACJ;8CACH;;;;;;;;;;;;wBAMF,cAAc,0BACb,8OAAC;4BAAK,UAAU,WAAW,YAAY,CAAC;4BAAe,WAAU;;8CAC/D,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC,iIAAA,CAAA,QAAK;oDACH,GAAG,WAAW,QAAQ,CAAC,QAAQ;oDAChC,MAAK;oDACL,aAAY;oDACZ,WAAU;oDACV,OAAO,WAAW,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE;;;;;;;;;;;;;;;;;;8CAKhD,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC,iIAAA,CAAA,QAAK;oDACH,GAAG,WAAW,QAAQ,CAAC,WAAW;oDACnC,MAAM,eAAe,SAAS;oDAC9B,aAAY;oDACZ,WAAU;oDACV,OAAO,WAAW,SAAS,CAAC,MAAM,CAAC,QAAQ,EAAE;;;;;;8DAE/C,8OAAC;oDACC,MAAK;oDACL,SAAS,IAAM,gBAAgB,CAAC;oDAChC,WAAU;8DAET,6BAAe,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;6EAAe,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8CAKtE,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,WAAU;oCACV,SAAS;8CACV;;;;;;;;;;;;wBAOJ,cAAc,0BACb,8OAAC;4BAAK,UAAU,WAAW,YAAY,CAAC;4BAAe,WAAU;;8CAC/D,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC,iIAAA,CAAA,QAAK;oDACH,GAAG,WAAW,QAAQ,CAAC,WAAW;oDACnC,MAAK;oDACL,aAAY;oDACZ,WAAU;oDACV,OAAO,WAAW,SAAS,CAAC,MAAM,CAAC,QAAQ,EAAE;;;;;;;;;;;;;;;;;;8CAKnD,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC,iIAAA,CAAA,QAAK;oDACH,GAAG,WAAW,QAAQ,CAAC,QAAQ;oDAChC,MAAK;oDACL,aAAY;oDACZ,WAAU;oDACV,OAAO,WAAW,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE;;;;;;;;;;;;;;;;;;8CAKhD,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC,iIAAA,CAAA,QAAK;oDACH,GAAG,WAAW,QAAQ,CAAC,WAAW;oDACnC,MAAM,eAAe,SAAS;oDAC9B,aAAY;oDACZ,WAAU;oDACV,OAAO,WAAW,SAAS,CAAC,MAAM,CAAC,QAAQ,EAAE;;;;;;8DAE/C,8OAAC;oDACC,MAAK;oDACL,SAAS,IAAM,gBAAgB,CAAC;oDAChC,WAAU;8DAET,6BAAe,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;6EAAe,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8CAKtE,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC,iIAAA,CAAA,QAAK;oDACH,GAAG,WAAW,QAAQ,CAAC,kBAAkB;oDAC1C,MAAM,sBAAsB,SAAS;oDACrC,aAAY;oDACZ,WAAU;oDACV,OAAO,WAAW,SAAS,CAAC,MAAM,CAAC,eAAe,EAAE;;;;;;8DAEtD,8OAAC;oDACC,MAAK;oDACL,SAAS,IAAM,uBAAuB,CAAC;oDACvC,WAAU;8DAET,oCAAsB,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;6EAAe,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8CAK7E,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,WAAU;oCACV,SAAS;8CACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 762, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Bayt/bayt-web/src/components/layout/Header.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { Search, Plus, Menu, X, User, Heart, MessageCircle, Bell } from 'lucide-react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { AuthModal } from '@/components/auth/AuthModal'\nimport { Button } from '@/components/ui/Button'\n\nexport function Header() {\n  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false)\n  const [authModalTab, setAuthModalTab] = useState<'signin' | 'signup'>('signin')\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)\n  const { user, signOut } = useAuth()\n\n  const handleAuthClick = (tab: 'signin' | 'signup') => {\n    setAuthModalTab(tab)\n    setIsAuthModalOpen(true)\n  }\n\n  return (\n    <>\n      <header className=\"bg-white shadow-sm border-b sticky top-0 z-40\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-16\">\n            {/* Logo */}\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-lg\">B</span>\n              </div>\n              <span className=\"text-xl font-bold text-gray-900\">Bayt</span>\n            </Link>\n\n            {/* Search Bar - Desktop */}\n            <div className=\"hidden md:flex flex-1 max-w-lg mx-8\">\n              <div className=\"relative w-full\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"Search for anything...\"\n                  className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              </div>\n            </div>\n\n            {/* Navigation - Desktop */}\n            <div className=\"hidden md:flex items-center space-x-4\">\n              {user ? (\n                <>\n                  <Link href=\"/sell\">\n                    <Button className=\"flex items-center space-x-2\">\n                      <Plus className=\"w-4 h-4\" />\n                      <span>Sell</span>\n                    </Button>\n                  </Link>\n                  \n                  <Link href=\"/favorites\" className=\"p-2 text-gray-600 hover:text-gray-900\">\n                    <Heart className=\"w-6 h-6\" />\n                  </Link>\n                  \n                  <Link href=\"/messages\" className=\"p-2 text-gray-600 hover:text-gray-900\">\n                    <MessageCircle className=\"w-6 h-6\" />\n                  </Link>\n                  \n                  <Link href=\"/notifications\" className=\"p-2 text-gray-600 hover:text-gray-900\">\n                    <Bell className=\"w-6 h-6\" />\n                  </Link>\n\n                  <div className=\"relative group\">\n                    <button className=\"flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100\">\n                      <div className=\"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center\">\n                        {user.avatar_url ? (\n                          <img\n                            src={user.avatar_url}\n                            alt={user.full_name}\n                            className=\"w-8 h-8 rounded-full object-cover\"\n                          />\n                        ) : (\n                          <User className=\"w-4 h-4 text-gray-600\" />\n                        )}\n                      </div>\n                      <span className=\"text-sm font-medium text-gray-700\">\n                        {user.full_name}\n                      </span>\n                    </button>\n                    \n                    {/* Dropdown Menu */}\n                    <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200\">\n                      <Link\n                        href=\"/profile\"\n                        className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                      >\n                        My Profile\n                      </Link>\n                      <Link\n                        href=\"/my-listings\"\n                        className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                      >\n                        My Listings\n                      </Link>\n                      <Link\n                        href=\"/settings\"\n                        className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                      >\n                        Settings\n                      </Link>\n                      <button\n                        onClick={signOut}\n                        className=\"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                      >\n                        Sign Out\n                      </button>\n                    </div>\n                  </div>\n                </>\n              ) : (\n                <div className=\"flex items-center space-x-3\">\n                  <Button\n                    variant=\"ghost\"\n                    onClick={() => handleAuthClick('signin')}\n                  >\n                    Sign In\n                  </Button>\n                  <Button onClick={() => handleAuthClick('signup')}>\n                    Sign Up\n                  </Button>\n                </div>\n              )}\n            </div>\n\n            {/* Mobile Menu Button */}\n            <button\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n              className=\"md:hidden p-2 text-gray-600 hover:text-gray-900\"\n            >\n              {isMobileMenuOpen ? (\n                <X className=\"w-6 h-6\" />\n              ) : (\n                <Menu className=\"w-6 h-6\" />\n              )}\n            </button>\n          </div>\n\n          {/* Search Bar - Mobile */}\n          <div className=\"md:hidden pb-4\">\n            <div className=\"relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search for anything...\"\n                className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile Menu */}\n        {isMobileMenuOpen && (\n          <div className=\"md:hidden bg-white border-t\">\n            <div className=\"px-4 py-2 space-y-2\">\n              {user ? (\n                <>\n                  <Link\n                    href=\"/sell\"\n                    className=\"flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-100\"\n                  >\n                    <Plus className=\"w-5 h-5\" />\n                    <span>Sell Item</span>\n                  </Link>\n                  <Link\n                    href=\"/favorites\"\n                    className=\"flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-100\"\n                  >\n                    <Heart className=\"w-5 h-5\" />\n                    <span>Favorites</span>\n                  </Link>\n                  <Link\n                    href=\"/messages\"\n                    className=\"flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-100\"\n                  >\n                    <MessageCircle className=\"w-5 h-5\" />\n                    <span>Messages</span>\n                  </Link>\n                  <Link\n                    href=\"/profile\"\n                    className=\"flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-100\"\n                  >\n                    <User className=\"w-5 h-5\" />\n                    <span>Profile</span>\n                  </Link>\n                  <button\n                    onClick={signOut}\n                    className=\"flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-100 w-full text-left\"\n                  >\n                    <span>Sign Out</span>\n                  </button>\n                </>\n              ) : (\n                <div className=\"space-y-2\">\n                  <Button\n                    variant=\"ghost\"\n                    className=\"w-full justify-start\"\n                    onClick={() => handleAuthClick('signin')}\n                  >\n                    Sign In\n                  </Button>\n                  <Button\n                    className=\"w-full\"\n                    onClick={() => handleAuthClick('signup')}\n                  >\n                    Sign Up\n                  </Button>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </header>\n\n      <AuthModal\n        isOpen={isAuthModalOpen}\n        onClose={() => setIsAuthModalOpen(false)}\n        defaultTab={authModalTab}\n      />\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAPA;;;;;;;;AASO,SAAS;IACd,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,MAAM,kBAAkB,CAAC;QACvB,gBAAgB;QAChB,mBAAmB;IACrB;IAEA,qBACE;;0BACE,8OAAC;gBAAO,WAAU;;kCAChB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;;0DACvB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;0DAEjD,8OAAC;gDAAK,WAAU;0DAAkC;;;;;;;;;;;;kDAIpD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,WAAU;;;;;;;;;;;;;;;;;kDAMhB,8OAAC;wCAAI,WAAU;kDACZ,qBACC;;8DACE,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;8DACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDAAC,WAAU;;0EAChB,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;8DAIV,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAa,WAAU;8DAChC,cAAA,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;8DAGnB,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAY,WAAU;8DAC/B,cAAA,8OAAC,wNAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;;;;;;8DAG3B,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAiB,WAAU;8DACpC,cAAA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAGlB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAO,WAAU;;8EAChB,8OAAC;oEAAI,WAAU;8EACZ,KAAK,UAAU,iBACd,8OAAC;wEACC,KAAK,KAAK,UAAU;wEACpB,KAAK,KAAK,SAAS;wEACnB,WAAU;;;;;6FAGZ,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;8EAGpB,8OAAC;oEAAK,WAAU;8EACb,KAAK,SAAS;;;;;;;;;;;;sEAKnB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,4JAAA,CAAA,UAAI;oEACH,MAAK;oEACL,WAAU;8EACX;;;;;;8EAGD,8OAAC,4JAAA,CAAA,UAAI;oEACH,MAAK;oEACL,WAAU;8EACX;;;;;;8EAGD,8OAAC,4JAAA,CAAA,UAAI;oEACH,MAAK;oEACL,WAAU;8EACX;;;;;;8EAGD,8OAAC;oEACC,SAAS;oEACT,WAAU;8EACX;;;;;;;;;;;;;;;;;;;yEAOP,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,SAAS,IAAM,gBAAgB;8DAChC;;;;;;8DAGD,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAS,IAAM,gBAAgB;8DAAW;;;;;;;;;;;;;;;;;kDAQxD,8OAAC;wCACC,SAAS,IAAM,oBAAoB,CAAC;wCACpC,WAAU;kDAET,iCACC,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;iEAEb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAMtB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;;oBAOjB,kCACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,qBACC;;kDACE,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;0DACzB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC;sDAAK;;;;;;;;;;;;6DAIV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;wCACV,SAAS,IAAM,gBAAgB;kDAChC;;;;;;kDAGD,8OAAC,kIAAA,CAAA,SAAM;wCACL,WAAU;wCACV,SAAS,IAAM,gBAAgB;kDAChC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUb,8OAAC,uIAAA,CAAA,YAAS;gBACR,QAAQ;gBACR,SAAS,IAAM,mBAAmB;gBAClC,YAAY;;;;;;;;AAIpB", "debugId": null}}, {"offset": {"line": 1321, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Bayt/bayt-web/src/components/home/<USER>"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport Link from 'next/link'\nimport { \n  Smartphone, \n  Car, \n  Home, \n  Shirt, \n  Sofa, \n  <PERSON><PERSON><PERSON>, \n  Book, \n  Wrench, \n  Briefcase, \n  MoreHorizontal \n} from 'lucide-react'\nimport { supabase, Category } from '@/lib/supabase'\n\nconst iconMap = {\n  smartphone: Smartphone,\n  car: Car,\n  home: Home,\n  shirt: Shirt,\n  sofa: Sofa,\n  dumbbell: Dumbbell,\n  book: Book,\n  wrench: Wrench,\n  briefcase: Briefcase,\n  'more-horizontal': MoreHorizontal,\n}\n\nexport function CategoryGrid() {\n  const [categories, setCategories] = useState<Category[]>([])\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    fetchCategories()\n  }, [])\n\n  const fetchCategories = async () => {\n    try {\n      const { data, error } = await supabase\n        .from('categories')\n        .select('*')\n        .is('parent_id', null)\n        .eq('is_active', true)\n        .order('sort_order')\n\n      if (error) throw error\n      setCategories(data || [])\n    } catch (error) {\n      console.error('Error fetching categories:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  if (loading) {\n    return (\n      <section className=\"py-16 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900\">Browse Categories</h2>\n            <p className=\"mt-4 text-lg text-gray-600\">\n              Find what you're looking for in our popular categories\n            </p>\n          </div>\n          <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6\">\n            {Array.from({ length: 10 }).map((_, index) => (\n              <div key={index} className=\"animate-pulse\">\n                <div className=\"bg-gray-200 rounded-lg p-6 text-center h-32\"></div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n    )\n  }\n\n  return (\n    <section className=\"py-16 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl font-bold text-gray-900\">Browse Categories</h2>\n          <p className=\"mt-4 text-lg text-gray-600\">\n            Find what you're looking for in our popular categories\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6\">\n          {categories.map((category) => {\n            const IconComponent = iconMap[category.icon as keyof typeof iconMap] || MoreHorizontal\n            \n            return (\n              <Link\n                key={category.id}\n                href={`/category/${category.id}`}\n                className=\"group bg-gray-50 rounded-lg p-6 text-center hover:bg-blue-50 hover:shadow-md transition-all duration-200\"\n              >\n                <div className=\"flex justify-center mb-4\">\n                  <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center group-hover:bg-blue-200 transition-colors\">\n                    <IconComponent className=\"w-6 h-6 text-blue-600\" />\n                  </div>\n                </div>\n                <h3 className=\"font-medium text-gray-900 group-hover:text-blue-600 transition-colors\">\n                  {category.name}\n                </h3>\n                <p className=\"text-sm text-gray-500 mt-1\">\n                  {category.name_ar}\n                </p>\n              </Link>\n            )\n          })}\n        </div>\n\n        <div className=\"text-center mt-12\">\n          <Link\n            href=\"/categories\"\n            className=\"inline-flex items-center px-6 py-3 border border-gray-300 rounded-md text-base font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors\"\n          >\n            View All Categories\n          </Link>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAhBA;;;;;;AAkBA,MAAM,UAAU;IACd,YAAY,8MAAA,CAAA,aAAU;IACtB,KAAK,gMAAA,CAAA,MAAG;IACR,MAAM,mMAAA,CAAA,OAAI;IACV,OAAO,oMAAA,CAAA,QAAK;IACZ,MAAM,kMAAA,CAAA,OAAI;IACV,UAAU,0MAAA,CAAA,WAAQ;IAClB,MAAM,kMAAA,CAAA,OAAI;IACV,QAAQ,sMAAA,CAAA,SAAM;IACd,WAAW,4MAAA,CAAA,YAAS;IACpB,mBAAmB,gNAAA,CAAA,iBAAc;AACnC;AAEO,SAAS;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa,MAChB,EAAE,CAAC,aAAa,MAChB,KAAK,CAAC;YAET,IAAI,OAAO,MAAM;YACjB,cAAc,QAAQ,EAAE;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAQ,WAAU;sBACjB,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAI5C,8OAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAG,GAAG,GAAG,CAAC,CAAC,GAAG,sBAClC,8OAAC;gCAAgB,WAAU;0CACzB,cAAA,8OAAC;oCAAI,WAAU;;;;;;+BADP;;;;;;;;;;;;;;;;;;;;;IAQtB;IAEA,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAK5C,8OAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC;wBACf,MAAM,gBAAgB,OAAO,CAAC,SAAS,IAAI,CAAyB,IAAI,gNAAA,CAAA,iBAAc;wBAEtF,qBACE,8OAAC,4JAAA,CAAA,UAAI;4BAEH,MAAM,CAAC,UAAU,EAAE,SAAS,EAAE,EAAE;4BAChC,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAc,WAAU;;;;;;;;;;;;;;;;8CAG7B,8OAAC;oCAAG,WAAU;8CACX,SAAS,IAAI;;;;;;8CAEhB,8OAAC;oCAAE,WAAU;8CACV,SAAS,OAAO;;;;;;;2BAbd,SAAS,EAAE;;;;;oBAiBtB;;;;;;8BAGF,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 1560, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Bayt/bayt-web/src/components/home/<USER>"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport Link from 'next/link'\nimport { Heart, MapPin, Eye } from 'lucide-react'\nimport { supabase, Product } from '@/lib/supabase'\nimport { formatPrice, formatRelativeTime, getImageUrl } from '@/lib/utils'\n\nexport function FeaturedProducts() {\n  const [products, setProducts] = useState<Product[]>([])\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    fetchFeaturedProducts()\n  }, [])\n\n  const fetchFeaturedProducts = async () => {\n    try {\n      const { data, error } = await supabase\n        .from('products')\n        .select(`\n          *,\n          category:categories(*),\n          user:users(*)\n        `)\n        .eq('is_active', true)\n        .eq('is_sold', false)\n        .order('created_at', { ascending: false })\n        .limit(8)\n\n      if (error) throw error\n      setProducts(data || [])\n    } catch (error) {\n      console.error('Error fetching featured products:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  if (loading) {\n    return (\n      <section className=\"py-16 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900\">Latest Listings</h2>\n            <p className=\"mt-4 text-lg text-gray-600\">\n              Discover the newest items added to our marketplace\n            </p>\n          </div>\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6\">\n            {Array.from({ length: 8 }).map((_, index) => (\n              <div key={index} className=\"animate-pulse\">\n                <div className=\"bg-white rounded-lg shadow-sm overflow-hidden\">\n                  <div className=\"bg-gray-200 h-48\"></div>\n                  <div className=\"p-4 space-y-3\">\n                    <div className=\"bg-gray-200 h-4 rounded\"></div>\n                    <div className=\"bg-gray-200 h-4 rounded w-3/4\"></div>\n                    <div className=\"bg-gray-200 h-4 rounded w-1/2\"></div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n    )\n  }\n\n  return (\n    <section className=\"py-16 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl font-bold text-gray-900\">Latest Listings</h2>\n          <p className=\"mt-4 text-lg text-gray-600\">\n            Discover the newest items added to our marketplace\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6\">\n          {products.map((product) => (\n            <ProductCard key={product.id} product={product} />\n          ))}\n        </div>\n\n        <div className=\"text-center mt-12\">\n          <Link\n            href=\"/products\"\n            className=\"inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-md text-base font-medium hover:bg-blue-700 transition-colors\"\n          >\n            View All Products\n          </Link>\n        </div>\n      </div>\n    </section>\n  )\n}\n\nfunction ProductCard({ product }: { product: Product }) {\n  const [isFavorited, setIsFavorited] = useState(false)\n\n  const handleFavoriteClick = (e: React.MouseEvent) => {\n    e.preventDefault()\n    e.stopPropagation()\n    setIsFavorited(!isFavorited)\n    // TODO: Implement favorite functionality\n  }\n\n  const mainImage = product.images && product.images.length > 0 \n    ? getImageUrl(product.images[0]) \n    : '/placeholder-image.jpg'\n\n  return (\n    <Link href={`/product/${product.id}`} className=\"group\">\n      <div className=\"bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow\">\n        <div className=\"relative\">\n          <img\n            src={mainImage}\n            alt={product.title}\n            className=\"w-full h-48 object-cover group-hover:scale-105 transition-transform duration-200\"\n          />\n          <button\n            onClick={handleFavoriteClick}\n            className=\"absolute top-3 right-3 p-2 bg-white rounded-full shadow-sm hover:shadow-md transition-shadow\"\n          >\n            <Heart\n              className={`w-4 h-4 ${\n                isFavorited ? 'text-red-500 fill-current' : 'text-gray-400'\n              }`}\n            />\n          </button>\n          {product.is_featured && (\n            <div className=\"absolute top-3 left-3 bg-yellow-400 text-yellow-900 px-2 py-1 rounded text-xs font-medium\">\n              Featured\n            </div>\n          )}\n        </div>\n\n        <div className=\"p-4\">\n          <h3 className=\"font-medium text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-2\">\n            {product.title}\n          </h3>\n          \n          <div className=\"mt-2 flex items-center justify-between\">\n            <span className=\"text-lg font-bold text-blue-600\">\n              {formatPrice(product.price, product.currency)}\n            </span>\n            <span className=\"text-sm text-gray-500 capitalize\">\n              {product.condition.replace('_', ' ')}\n            </span>\n          </div>\n\n          <div className=\"mt-3 flex items-center text-sm text-gray-500\">\n            <MapPin className=\"w-4 h-4 mr-1\" />\n            <span className=\"truncate\">{product.location}</span>\n          </div>\n\n          <div className=\"mt-3 flex items-center justify-between text-sm text-gray-500\">\n            <span>{formatRelativeTime(product.created_at)}</span>\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"flex items-center\">\n                <Eye className=\"w-4 h-4 mr-1\" />\n                <span>{product.views_count}</span>\n              </div>\n              <div className=\"flex items-center\">\n                <Heart className=\"w-4 h-4 mr-1\" />\n                <span>{product.favorites_count}</span>\n              </div>\n            </div>\n          </div>\n\n          {product.user && (\n            <div className=\"mt-3 pt-3 border-t border-gray-100 flex items-center\">\n              <div className=\"w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center mr-2\">\n                {product.user.avatar_url ? (\n                  <img\n                    src={product.user.avatar_url}\n                    alt={product.user.full_name}\n                    className=\"w-6 h-6 rounded-full object-cover\"\n                  />\n                ) : (\n                  <span className=\"text-xs text-gray-600\">\n                    {product.user.full_name.charAt(0)}\n                  </span>\n                )}\n              </div>\n              <span className=\"text-sm text-gray-600 truncate\">\n                {product.user.full_name}\n              </span>\n              {product.user.rating && (\n                <div className=\"ml-auto flex items-center\">\n                  <span className=\"text-xs text-yellow-500\">★</span>\n                  <span className=\"text-xs text-gray-600 ml-1\">\n                    {product.user.rating.toFixed(1)}\n                  </span>\n                </div>\n              )}\n            </div>\n          )}\n        </div>\n      </div>\n    </Link>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AANA;;;;;;;AAQO,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,wBAAwB;QAC5B,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,CAAC;;;;QAIT,CAAC,EACA,EAAE,CAAC,aAAa,MAChB,EAAE,CAAC,WAAW,OACd,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM,GACvC,KAAK,CAAC;YAET,IAAI,OAAO,MAAM;YACjB,YAAY,QAAQ,EAAE;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAQ,WAAU;sBACjB,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAI5C,8OAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,8OAAC;gCAAgB,WAAU;0CACzB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;+BANX;;;;;;;;;;;;;;;;;;;;;IAetB;IAEA,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAK5C,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;4BAA6B,SAAS;2BAArB,QAAQ,EAAE;;;;;;;;;;8BAIhC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;;;;;;;;;;;AAOX;AAEA,SAAS,YAAY,EAAE,OAAO,EAAwB;IACpD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,sBAAsB,CAAC;QAC3B,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,eAAe,CAAC;IAChB,yCAAyC;IAC3C;IAEA,MAAM,YAAY,QAAQ,MAAM,IAAI,QAAQ,MAAM,CAAC,MAAM,GAAG,IACxD,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,MAAM,CAAC,EAAE,IAC7B;IAEJ,qBACE,8OAAC,4JAAA,CAAA,UAAI;QAAC,MAAM,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE;QAAE,WAAU;kBAC9C,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,KAAK;4BACL,KAAK,QAAQ,KAAK;4BAClB,WAAU;;;;;;sCAEZ,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,oMAAA,CAAA,QAAK;gCACJ,WAAW,CAAC,QAAQ,EAClB,cAAc,8BAA8B,iBAC5C;;;;;;;;;;;wBAGL,QAAQ,WAAW,kBAClB,8OAAC;4BAAI,WAAU;sCAA4F;;;;;;;;;;;;8BAM/G,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX,QAAQ,KAAK;;;;;;sCAGhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CACb,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,KAAK,EAAE,QAAQ,QAAQ;;;;;;8CAE9C,8OAAC;oCAAK,WAAU;8CACb,QAAQ,SAAS,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;sCAIpC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;oCAAK,WAAU;8CAAY,QAAQ,QAAQ;;;;;;;;;;;;sCAG9C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAM,CAAA,GAAA,mHAAA,CAAA,qBAAkB,AAAD,EAAE,QAAQ,UAAU;;;;;;8CAC5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;8DACf,8OAAC;8DAAM,QAAQ,WAAW;;;;;;;;;;;;sDAE5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;8DAAM,QAAQ,eAAe;;;;;;;;;;;;;;;;;;;;;;;;wBAKnC,QAAQ,IAAI,kBACX,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACZ,QAAQ,IAAI,CAAC,UAAU,iBACtB,8OAAC;wCACC,KAAK,QAAQ,IAAI,CAAC,UAAU;wCAC5B,KAAK,QAAQ,IAAI,CAAC,SAAS;wCAC3B,WAAU;;;;;6DAGZ,8OAAC;wCAAK,WAAU;kDACb,QAAQ,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;;;;;;;;;;;8CAIrC,8OAAC;oCAAK,WAAU;8CACb,QAAQ,IAAI,CAAC,SAAS;;;;;;gCAExB,QAAQ,IAAI,CAAC,MAAM,kBAClB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAA0B;;;;;;sDAC1C,8OAAC;4CAAK,WAAU;sDACb,QAAQ,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjD", "debugId": null}}, {"offset": {"line": 2054, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Bayt/bayt-web/src/components/home/<USER>"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Search, MapPin } from 'lucide-react'\nimport { Button } from '@/components/ui/Button'\n\nexport function HeroSection() {\n  const [searchQuery, setSearchQuery] = useState('')\n  const [location, setLocation] = useState('')\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault()\n    // TODO: Implement search functionality\n    console.log('Search:', { searchQuery, location })\n  }\n\n  return (\n    <section className=\"bg-gradient-to-br from-blue-600 to-blue-800 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 lg:py-24\">\n        <div className=\"text-center mb-12\">\n          <h1 className=\"text-4xl lg:text-6xl font-bold mb-6\">\n            Find Everything You Need in{' '}\n            <span className=\"text-yellow-300\">Syria</span>\n          </h1>\n          <p className=\"text-xl lg:text-2xl text-blue-100 max-w-3xl mx-auto\">\n            Buy and sell with confidence on Syria's most trusted marketplace\n          </p>\n        </div>\n\n        <div className=\"max-w-4xl mx-auto\">\n          <form onSubmit={handleSearch} className=\"bg-white rounded-lg shadow-xl p-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              {/* Search Input */}\n              <div className=\"md:col-span-2\">\n                <div className=\"relative\">\n                  <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n                  <input\n                    type=\"text\"\n                    placeholder=\"What are you looking for?\"\n                    value={searchQuery}\n                    onChange={(e) => setSearchQuery(e.target.value)}\n                    className=\"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900\"\n                  />\n                </div>\n              </div>\n\n              {/* Location Input */}\n              <div>\n                <div className=\"relative\">\n                  <MapPin className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n                  <input\n                    type=\"text\"\n                    placeholder=\"Location\"\n                    value={location}\n                    onChange={(e) => setLocation(e.target.value)}\n                    className=\"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900\"\n                  />\n                </div>\n              </div>\n            </div>\n\n            <div className=\"mt-6 flex flex-col sm:flex-row gap-4\">\n              <Button type=\"submit\" size=\"lg\" className=\"flex-1\">\n                Search\n              </Button>\n              <Button type=\"button\" variant=\"outline\" size=\"lg\" className=\"text-gray-700\">\n                Advanced Search\n              </Button>\n            </div>\n          </form>\n        </div>\n\n        {/* Stats */}\n        <div className=\"mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 text-center\">\n          <div>\n            <div className=\"text-3xl font-bold text-yellow-300\">10K+</div>\n            <div className=\"text-blue-100\">Active Listings</div>\n          </div>\n          <div>\n            <div className=\"text-3xl font-bold text-yellow-300\">5K+</div>\n            <div className=\"text-blue-100\">Happy Users</div>\n          </div>\n          <div>\n            <div className=\"text-3xl font-bold text-yellow-300\">15+</div>\n            <div className=\"text-blue-100\">Cities</div>\n          </div>\n          <div>\n            <div className=\"text-3xl font-bold text-yellow-300\">24/7</div>\n            <div className=\"text-blue-100\">Support</div>\n          </div>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAMO,SAAS;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,uCAAuC;QACvC,QAAQ,GAAG,CAAC,WAAW;YAAE;YAAa;QAAS;IACjD;IAEA,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCAAsC;gCACtB;8CAC5B,8OAAC;oCAAK,WAAU;8CAAkB;;;;;;;;;;;;sCAEpC,8OAAC;4BAAE,WAAU;sCAAsD;;;;;;;;;;;;8BAKrE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oDAC9C,WAAU;;;;;;;;;;;;;;;;;kDAMhB,8OAAC;kDACC,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oDAC3C,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAMlB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,MAAK;wCAAK,WAAU;kDAAS;;;;;;kDAGnD,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,SAAQ;wCAAU,MAAK;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;8BAQlF,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;8CAAqC;;;;;;8CACpD,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;sCAEjC,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;8CAAqC;;;;;;8CACpD,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;sCAEjC,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;8CAAqC;;;;;;8CACpD,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;sCAEjC,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;8CAAqC;;;;;;8CACpD,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3C", "debugId": null}}]}