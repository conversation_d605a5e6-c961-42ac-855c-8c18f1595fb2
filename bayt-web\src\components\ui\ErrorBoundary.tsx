'use client'

import React, { Component, ErrorInfo, ReactNode } from 'react'
import { motion } from 'framer-motion'
import { AlertTriangle, RefreshCw, Home, Bug } from 'lucide-react'
import { Button } from '@/components/ui/Button'

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
}

interface State {
  hasError: boolean
  error?: Error
  errorInfo?: ErrorInfo
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo)
    
    this.setState({ error, errorInfo })
    
    // Call custom error handler
    this.props.onError?.(error, errorInfo)
    
    // Log to error reporting service
    this.logErrorToService(error, errorInfo)
  }

  logErrorToService = (error: Error, errorInfo: ErrorInfo) => {
    // In production, send to error reporting service like Sentry
    const errorData = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    }
    
    console.log('Error logged:', errorData)
    
    // Example: Send to analytics
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', 'exception', {
        description: error.message,
        fatal: false
      })
    }
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined })
  }

  handleGoHome = () => {
    window.location.href = '/'
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      return (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-blue-50 p-4"
        >
          <div className="max-w-md w-full bg-white rounded-2xl shadow-xl p-8 text-center">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2 }}
              className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6"
            >
              <AlertTriangle className="w-8 h-8 text-red-500" />
            </motion.div>

            <motion.h1
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
              className="text-2xl font-bold text-gray-900 mb-4"
            >
              Oops! Something went wrong
            </motion.h1>

            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.4 }}
              className="text-gray-600 mb-6"
            >
              We're sorry for the inconvenience. The page encountered an unexpected error.
            </motion.p>

            {process.env.NODE_ENV === 'development' && this.state.error && (
              <motion.details
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.5 }}
                className="text-left bg-gray-50 rounded-lg p-4 mb-6"
              >
                <summary className="cursor-pointer text-sm font-medium text-gray-700 mb-2">
                  <Bug className="w-4 h-4 inline mr-2" />
                  Error Details (Development)
                </summary>
                <pre className="text-xs text-red-600 overflow-auto max-h-32">
                  {this.state.error.message}
                  {'\n\n'}
                  {this.state.error.stack}
                </pre>
              </motion.details>
            )}

            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="flex flex-col sm:flex-row gap-3"
            >
              <Button
                onClick={this.handleRetry}
                className="flex-1 bg-blue-600 hover:bg-blue-700"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Try Again
              </Button>
              <Button
                onClick={this.handleGoHome}
                variant="outline"
                className="flex-1"
              >
                <Home className="w-4 h-4 mr-2" />
                Go Home
              </Button>
            </motion.div>

            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.7 }}
              className="text-sm text-gray-500 mt-6"
            >
              If the problem persists, please contact our support team.
            </motion.p>
          </div>
        </motion.div>
      )
    }

    return this.props.children
  }
}

// Hook for error handling
export function useErrorHandler() {
  const handleError = (error: Error, errorInfo?: any) => {
    console.error('Error handled:', error, errorInfo)
    
    // Log to error service
    const errorData = {
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      ...errorInfo
    }
    
    // Send to analytics
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', 'exception', {
        description: error.message,
        fatal: false
      })
    }
  }

  return { handleError }
}

// Async error boundary for handling promise rejections
export function AsyncErrorBoundary({ children }: { children: ReactNode }) {
  React.useEffect(() => {
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.error('Unhandled promise rejection:', event.reason)
      
      // Log to error service
      const errorData = {
        type: 'unhandled_promise_rejection',
        reason: event.reason?.toString() || 'Unknown error',
        timestamp: new Date().toISOString(),
        url: window.location.href
      }
      
      console.log('Promise rejection logged:', errorData)
    }

    window.addEventListener('unhandledrejection', handleUnhandledRejection)
    
    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection)
    }
  }, [])

  return <>{children}</>
}

// Component-specific error boundary
export function ComponentErrorBoundary({ 
  children, 
  componentName,
  fallback 
}: { 
  children: ReactNode
  componentName: string
  fallback?: ReactNode
}) {
  return (
    <ErrorBoundary
      fallback={fallback || (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center gap-2 text-red-700">
            <AlertTriangle className="w-4 h-4" />
            <span className="font-medium">Error in {componentName}</span>
          </div>
          <p className="text-sm text-red-600 mt-1">
            This component failed to load. Please refresh the page.
          </p>
        </div>
      )}
      onError={(error, errorInfo) => {
        console.error(`Error in ${componentName}:`, error, errorInfo)
      }}
    >
      {children}
    </ErrorBoundary>
  )
}

// Network error boundary for API calls
export function NetworkErrorBoundary({ children }: { children: ReactNode }) {
  const [hasNetworkError, setHasNetworkError] = React.useState(false)

  React.useEffect(() => {
    const handleOnline = () => setHasNetworkError(false)
    const handleOffline = () => setHasNetworkError(true)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  if (hasNetworkError) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg"
      >
        <div className="flex items-center gap-2 text-yellow-700">
          <AlertTriangle className="w-4 h-4" />
          <span className="font-medium">Network Error</span>
        </div>
        <p className="text-sm text-yellow-600 mt-1">
          You're currently offline. Some features may not work properly.
        </p>
      </motion.div>
    )
  }

  return <>{children}</>
}

// Global error handler setup
export function setupGlobalErrorHandling() {
  // Handle uncaught errors
  window.addEventListener('error', (event) => {
    console.error('Global error:', event.error)
    
    const errorData = {
      type: 'javascript_error',
      message: event.message,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      stack: event.error?.stack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    }
    
    console.log('Global error logged:', errorData)
  })

  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason)
    
    const errorData = {
      type: 'unhandled_promise_rejection',
      reason: event.reason?.toString() || 'Unknown error',
      timestamp: new Date().toISOString(),
      url: window.location.href
    }
    
    console.log('Promise rejection logged:', errorData)
  })
}
