export type Locale = 'en' | 'ar' | 'tr'

export const locales: Locale[] = ['en', 'ar', 'tr']

export const defaultLocale: Locale = 'en'

export const translations = {
  en: {
    // Navigation
    'nav.home': 'Home',
    'nav.search': 'Search',
    'nav.sell': 'Sell',
    'nav.messages': 'Messages',
    'nav.profile': 'Profile',
    'nav.favorites': 'Favorites',
    'nav.signin': 'Sign In',
    'nav.signup': 'Sign Up',
    'nav.signout': 'Sign Out',

    // Common
    'common.loading': 'Loading...',
    'common.error': 'Error',
    'common.success': 'Success',
    'common.cancel': 'Cancel',
    'common.save': 'Save',
    'common.edit': 'Edit',
    'common.delete': 'Delete',
    'common.view': 'View',
    'common.back': 'Back',
    'common.next': 'Next',
    'common.previous': 'Previous',
    'common.search': 'Search',
    'common.filter': 'Filter',
    'common.sort': 'Sort',
    'common.price': 'Price',
    'common.location': 'Location',
    'common.category': 'Category',
    'common.condition': 'Condition',
    'common.description': 'Description',
    'common.contact': 'Contact',
    'common.share': 'Share',
    'common.report': 'Report',

    // Home Page
    'home.hero.title': 'Find Everything You Need in Syria',
    'home.hero.subtitle': 'Buy and sell with confidence on Syria\'s most trusted marketplace',
    'home.categories.title': 'Browse Categories',
    'home.categories.subtitle': 'Find what you\'re looking for in our popular categories',
    'home.featured.title': 'Latest Listings',
    'home.featured.subtitle': 'Discover the newest items added to our marketplace',

    // Product
    'product.condition.new': 'New',
    'product.condition.like_new': 'Like New',
    'product.condition.good': 'Good',
    'product.condition.fair': 'Fair',
    'product.condition.poor': 'Poor',
    'product.contact_seller': 'Contact Seller',
    'product.make_offer': 'Make Offer',
    'product.seller_info': 'Seller Information',
    'product.views': 'views',
    'product.favorites': 'favorites',

    // Auth
    'auth.signin': 'Sign In',
    'auth.signup': 'Sign Up',
    'auth.email': 'Email',
    'auth.password': 'Password',
    'auth.confirm_password': 'Confirm Password',
    'auth.full_name': 'Full Name',
    'auth.forgot_password': 'Forgot Password?',
    'auth.no_account': 'Don\'t have an account?',
    'auth.have_account': 'Already have an account?',

    // Categories
    'category.electronics': 'Electronics',
    'category.vehicles': 'Vehicles',
    'category.real_estate': 'Real Estate',
    'category.fashion': 'Fashion & Beauty',
    'category.home_garden': 'Home & Garden',
    'category.sports': 'Sports & Hobbies',
    'category.books': 'Books & Education',
    'category.services': 'Services',
    'category.jobs': 'Jobs',
    'category.others': 'Others',
  },
  ar: {
    // Navigation
    'nav.home': 'الرئيسية',
    'nav.search': 'البحث',
    'nav.sell': 'بيع',
    'nav.messages': 'الرسائل',
    'nav.profile': 'الملف الشخصي',
    'nav.favorites': 'المفضلة',
    'nav.signin': 'تسجيل الدخول',
    'nav.signup': 'إنشاء حساب',
    'nav.signout': 'تسجيل الخروج',

    // Common
    'common.loading': 'جاري التحميل...',
    'common.error': 'خطأ',
    'common.success': 'نجح',
    'common.cancel': 'إلغاء',
    'common.save': 'حفظ',
    'common.edit': 'تعديل',
    'common.delete': 'حذف',
    'common.view': 'عرض',
    'common.back': 'رجوع',
    'common.next': 'التالي',
    'common.previous': 'السابق',
    'common.search': 'بحث',
    'common.filter': 'تصفية',
    'common.sort': 'ترتيب',
    'common.price': 'السعر',
    'common.location': 'الموقع',
    'common.category': 'الفئة',
    'common.condition': 'الحالة',
    'common.description': 'الوصف',
    'common.contact': 'اتصال',
    'common.share': 'مشاركة',
    'common.report': 'إبلاغ',

    // Home Page
    'home.hero.title': 'اعثر على كل ما تحتاجه في سوريا',
    'home.hero.subtitle': 'اشتر وبع بثقة في أكثر الأسواق الإلكترونية موثوقية في سوريا',
    'home.categories.title': 'تصفح الفئات',
    'home.categories.subtitle': 'اعثر على ما تبحث عنه في فئاتنا الشائعة',
    'home.featured.title': 'أحدث الإعلانات',
    'home.featured.subtitle': 'اكتشف أحدث العناصر المضافة إلى السوق',

    // Product
    'product.condition.new': 'جديد',
    'product.condition.like_new': 'مثل الجديد',
    'product.condition.good': 'جيد',
    'product.condition.fair': 'مقبول',
    'product.condition.poor': 'ضعيف',
    'product.contact_seller': 'اتصل بالبائع',
    'product.make_offer': 'قدم عرضاً',
    'product.seller_info': 'معلومات البائع',
    'product.views': 'مشاهدة',
    'product.favorites': 'مفضلة',

    // Auth
    'auth.signin': 'تسجيل الدخول',
    'auth.signup': 'إنشاء حساب',
    'auth.email': 'البريد الإلكتروني',
    'auth.password': 'كلمة المرور',
    'auth.confirm_password': 'تأكيد كلمة المرور',
    'auth.full_name': 'الاسم الكامل',
    'auth.forgot_password': 'نسيت كلمة المرور؟',
    'auth.no_account': 'ليس لديك حساب؟',
    'auth.have_account': 'لديك حساب بالفعل؟',

    // Categories
    'category.electronics': 'إلكترونيات',
    'category.vehicles': 'مركبات',
    'category.real_estate': 'عقارات',
    'category.fashion': 'أزياء وجمال',
    'category.home_garden': 'منزل وحديقة',
    'category.sports': 'رياضة وهوايات',
    'category.books': 'كتب وتعليم',
    'category.services': 'خدمات',
    'category.jobs': 'وظائف',
    'category.others': 'أخرى',
  },
  tr: {
    // Navigation
    'nav.home': 'Ana Sayfa',
    'nav.search': 'Ara',
    'nav.sell': 'Sat',
    'nav.messages': 'Mesajlar',
    'nav.profile': 'Profil',
    'nav.favorites': 'Favoriler',
    'nav.signin': 'Giriş Yap',
    'nav.signup': 'Kayıt Ol',
    'nav.signout': 'Çıkış Yap',

    // Common
    'common.loading': 'Yükleniyor...',
    'common.error': 'Hata',
    'common.success': 'Başarılı',
    'common.cancel': 'İptal',
    'common.save': 'Kaydet',
    'common.edit': 'Düzenle',
    'common.delete': 'Sil',
    'common.view': 'Görüntüle',
    'common.back': 'Geri',
    'common.next': 'İleri',
    'common.previous': 'Önceki',
    'common.search': 'Ara',
    'common.filter': 'Filtrele',
    'common.sort': 'Sırala',
    'common.price': 'Fiyat',
    'common.location': 'Konum',
    'common.category': 'Kategori',
    'common.condition': 'Durum',
    'common.description': 'Açıklama',
    'common.contact': 'İletişim',
    'common.share': 'Paylaş',
    'common.report': 'Şikayet Et',

    // Home Page
    'home.hero.title': 'Türkiye\'de İhtiyacınız Olan Her Şeyi Bulun',
    'home.hero.subtitle': 'Türkiye\'nin en güvenilir pazaryerinde güvenle alın ve satın',
    'home.categories.title': 'Kategorilere Göz Atın',
    'home.categories.subtitle': 'Popüler kategorilerimizde aradığınızı bulun',
    'home.featured.title': 'Son İlanlar',
    'home.featured.subtitle': 'Pazaryerimize eklenen en yeni ürünleri keşfedin',

    // Product
    'product.condition.new': 'Yeni',
    'product.condition.like_new': 'Sıfır Gibi',
    'product.condition.good': 'İyi',
    'product.condition.fair': 'Orta',
    'product.condition.poor': 'Kötü',
    'product.contact_seller': 'Satıcıyla İletişime Geç',
    'product.make_offer': 'Teklif Ver',
    'product.seller_info': 'Satıcı Bilgileri',
    'product.views': 'görüntüleme',
    'product.favorites': 'favori',

    // Auth
    'auth.signin': 'Giriş Yap',
    'auth.signup': 'Kayıt Ol',
    'auth.email': 'E-posta',
    'auth.password': 'Şifre',
    'auth.confirm_password': 'Şifreyi Onayla',
    'auth.full_name': 'Ad Soyad',
    'auth.forgot_password': 'Şifremi Unuttum?',
    'auth.no_account': 'Hesabınız yok mu?',
    'auth.have_account': 'Zaten hesabınız var mı?',

    // Categories
    'category.electronics': 'Elektronik',
    'category.vehicles': 'Araçlar',
    'category.real_estate': 'Emlak',
    'category.fashion': 'Moda ve Güzellik',
    'category.home_garden': 'Ev ve Bahçe',
    'category.sports': 'Spor ve Hobi',
    'category.books': 'Kitap ve Eğitim',
    'category.services': 'Hizmetler',
    'category.jobs': 'İş İlanları',
    'category.others': 'Diğer',
  },
}

export function getTranslation(locale: Locale, key: string): string {
  const keys = key.split('.')
  let value: any = translations[locale]
  
  for (const k of keys) {
    value = value?.[k]
  }
  
  return value || key
}

export function t(key: string, locale: Locale = defaultLocale): string {
  return getTranslation(locale, key)
}
