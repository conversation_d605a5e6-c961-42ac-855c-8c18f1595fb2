'use client'

import { useState, useRef, useEffect } from 'react'
import { motion, PanInfo, useMotionValue, useTransform } from 'framer-motion'
import { ArrowLeft, ArrowRight, Heart, Share2, MoreVertical } from 'lucide-react'

interface SwipeableCardProps {
  children: React.ReactNode
  onSwipeLeft?: () => void
  onSwipeRight?: () => void
  onSwipeUp?: () => void
  onSwipeDown?: () => void
  className?: string
}

export function SwipeableCard({
  children,
  onSwipeLeft,
  onSwipeRight,
  onSwipeUp,
  onSwipeDown,
  className = ''
}: SwipeableCardProps) {
  const [isExiting, setIsExiting] = useState(false)
  const x = useMotionValue(0)
  const y = useMotionValue(0)
  
  const rotateZ = useTransform(x, [-200, 200], [-15, 15])
  const opacity = useTransform(x, [-200, -100, 0, 100, 200], [0, 1, 1, 1, 0])

  const handleDragEnd = (event: any, info: PanInfo) => {
    const threshold = 100
    const velocity = 500

    if (Math.abs(info.offset.x) > threshold || Math.abs(info.velocity.x) > velocity) {
      setIsExiting(true)
      
      if (info.offset.x > 0) {
        onSwipeRight?.()
      } else {
        onSwipeLeft?.()
      }
    } else if (Math.abs(info.offset.y) > threshold || Math.abs(info.velocity.y) > velocity) {
      setIsExiting(true)
      
      if (info.offset.y > 0) {
        onSwipeDown?.()
      } else {
        onSwipeUp?.()
      }
    } else {
      // Snap back to center
      x.set(0)
      y.set(0)
    }
  }

  return (
    <motion.div
      className={`relative ${className}`}
      style={{ x, y, rotateZ, opacity }}
      drag
      dragConstraints={{ left: 0, right: 0, top: 0, bottom: 0 }}
      dragElastic={0.2}
      onDragEnd={handleDragEnd}
      animate={isExiting ? { x: x.get() > 0 ? 300 : -300, opacity: 0 } : {}}
      transition={{ duration: 0.3 }}
      whileDrag={{ scale: 1.05, zIndex: 1 }}
    >
      {children}
      
      {/* Swipe Indicators */}
      <motion.div
        className="absolute top-4 left-4 bg-green-500 text-white p-2 rounded-full"
        style={{ opacity: useTransform(x, [0, 100], [0, 1]) }}
      >
        <Heart className="w-4 h-4" />
      </motion.div>
      
      <motion.div
        className="absolute top-4 right-4 bg-red-500 text-white p-2 rounded-full"
        style={{ opacity: useTransform(x, [-100, 0], [1, 0]) }}
      >
        <ArrowLeft className="w-4 h-4" />
      </motion.div>
    </motion.div>
  )
}

// Pull to refresh component
export function PullToRefresh({
  children,
  onRefresh,
  threshold = 80
}: {
  children: React.ReactNode
  onRefresh: () => Promise<void>
  threshold?: number
}) {
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [pullDistance, setPullDistance] = useState(0)
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    let startY = 0
    let currentY = 0
    let isAtTop = false

    const handleTouchStart = (e: TouchEvent) => {
      if (containerRef.current?.scrollTop === 0) {
        isAtTop = true
        startY = e.touches[0].clientY
      }
    }

    const handleTouchMove = (e: TouchEvent) => {
      if (!isAtTop || isRefreshing) return

      currentY = e.touches[0].clientY
      const distance = Math.max(0, currentY - startY)
      
      if (distance > 0) {
        e.preventDefault()
        setPullDistance(Math.min(distance, threshold * 1.5))
      }
    }

    const handleTouchEnd = async () => {
      if (!isAtTop || isRefreshing) return

      if (pullDistance >= threshold) {
        setIsRefreshing(true)
        try {
          await onRefresh()
        } finally {
          setIsRefreshing(false)
        }
      }
      
      setPullDistance(0)
      isAtTop = false
    }

    const container = containerRef.current
    if (container) {
      container.addEventListener('touchstart', handleTouchStart, { passive: false })
      container.addEventListener('touchmove', handleTouchMove, { passive: false })
      container.addEventListener('touchend', handleTouchEnd)
    }

    return () => {
      if (container) {
        container.removeEventListener('touchstart', handleTouchStart)
        container.removeEventListener('touchmove', handleTouchMove)
        container.removeEventListener('touchend', handleTouchEnd)
      }
    }
  }, [onRefresh, threshold, isRefreshing, pullDistance])

  return (
    <div ref={containerRef} className="relative overflow-auto h-full">
      {/* Pull indicator */}
      <motion.div
        className="absolute top-0 left-0 right-0 flex items-center justify-center bg-blue-500 text-white z-10"
        style={{ height: pullDistance }}
        animate={{ opacity: pullDistance > 0 ? 1 : 0 }}
      >
        <motion.div
          animate={{ 
            rotate: isRefreshing ? 360 : pullDistance >= threshold ? 180 : 0 
          }}
          transition={{ 
            duration: isRefreshing ? 1 : 0.3,
            repeat: isRefreshing ? Infinity : 0,
            ease: "linear"
          }}
        >
          <ArrowLeft className="w-5 h-5" />
        </motion.div>
        <span className="ml-2 text-sm">
          {isRefreshing ? 'Refreshing...' : pullDistance >= threshold ? 'Release to refresh' : 'Pull to refresh'}
        </span>
      </motion.div>
      
      <div style={{ paddingTop: pullDistance }}>
        {children}
      </div>
    </div>
  )
}

// Bottom sheet component
export function BottomSheet({
  isOpen,
  onClose,
  children,
  snapPoints = [0.3, 0.6, 0.9]
}: {
  isOpen: boolean
  onClose: () => void
  children: React.ReactNode
  snapPoints?: number[]
}) {
  const [currentSnap, setCurrentSnap] = useState(0)
  const y = useMotionValue(0)

  const handleDragEnd = (event: any, info: PanInfo) => {
    const velocity = info.velocity.y
    const offset = info.offset.y
    
    if (velocity > 500 || offset > 100) {
      onClose()
      return
    }

    // Find closest snap point
    const windowHeight = window.innerHeight
    const currentPosition = (windowHeight - y.get()) / windowHeight
    
    let closestSnap = 0
    let minDistance = Infinity
    
    snapPoints.forEach((snap, index) => {
      const distance = Math.abs(currentPosition - snap)
      if (distance < minDistance) {
        minDistance = distance
        closestSnap = index
      }
    })
    
    setCurrentSnap(closestSnap)
  }

  return (
    <>
      {/* Backdrop */}
      <motion.div
        className="fixed inset-0 bg-black z-40"
        initial={{ opacity: 0 }}
        animate={{ opacity: isOpen ? 0.5 : 0 }}
        exit={{ opacity: 0 }}
        onClick={onClose}
        style={{ pointerEvents: isOpen ? 'auto' : 'none' }}
      />
      
      {/* Bottom Sheet */}
      <motion.div
        className="fixed bottom-0 left-0 right-0 bg-white rounded-t-3xl shadow-2xl z-50"
        style={{ y }}
        initial={{ y: '100%' }}
        animate={{ 
          y: isOpen ? `${(1 - snapPoints[currentSnap]) * 100}%` : '100%'
        }}
        exit={{ y: '100%' }}
        drag="y"
        dragConstraints={{ top: 0, bottom: 0 }}
        dragElastic={0.1}
        onDragEnd={handleDragEnd}
        transition={{ type: 'spring', damping: 30, stiffness: 300 }}
      >
        {/* Handle */}
        <div className="flex justify-center pt-4 pb-2">
          <div className="w-12 h-1 bg-gray-300 rounded-full" />
        </div>
        
        <div className="px-4 pb-4 max-h-[80vh] overflow-auto">
          {children}
        </div>
      </motion.div>
    </>
  )
}

// Floating Action Button with gestures
export function FloatingActionButton({
  icon: Icon,
  onClick,
  className = ''
}: {
  icon: React.ComponentType<any>
  onClick: () => void
  className?: string
}) {
  const [isDragging, setIsDragging] = useState(false)
  const x = useMotionValue(0)
  const y = useMotionValue(0)

  return (
    <motion.button
      className={`fixed bottom-6 right-6 w-14 h-14 bg-blue-600 text-white rounded-full shadow-lg z-40 flex items-center justify-center ${className}`}
      style={{ x, y }}
      drag
      dragConstraints={{ left: -200, right: 0, top: -400, bottom: 0 }}
      dragElastic={0.1}
      onDragStart={() => setIsDragging(true)}
      onDragEnd={() => setIsDragging(false)}
      onClick={!isDragging ? onClick : undefined}
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.9 }}
      whileDrag={{ scale: 1.2, zIndex: 50 }}
    >
      <Icon className="w-6 h-6" />
    </motion.button>
  )
}

// Haptic feedback hook
export function useHapticFeedback() {
  const vibrate = (pattern: number | number[]) => {
    if ('vibrate' in navigator) {
      navigator.vibrate(pattern)
    }
  }

  const lightImpact = () => vibrate(10)
  const mediumImpact = () => vibrate(20)
  const heavyImpact = () => vibrate(30)
  const success = () => vibrate([10, 50, 10])
  const warning = () => vibrate([20, 100, 20])
  const error = () => vibrate([50, 100, 50, 100, 50])

  return {
    lightImpact,
    mediumImpact,
    heavyImpact,
    success,
    warning,
    error
  }
}

// Touch gesture hook
export function useTouchGestures(
  elementRef: React.RefObject<HTMLElement>,
  callbacks: {
    onTap?: () => void
    onDoubleTap?: () => void
    onLongPress?: () => void
    onSwipeLeft?: () => void
    onSwipeRight?: () => void
    onSwipeUp?: () => void
    onSwipeDown?: () => void
    onPinch?: (scale: number) => void
  }
) {
  useEffect(() => {
    const element = elementRef.current
    if (!element) return

    let touchStartTime = 0
    let touchStartX = 0
    let touchStartY = 0
    let lastTapTime = 0
    let longPressTimer: NodeJS.Timeout

    const handleTouchStart = (e: TouchEvent) => {
      touchStartTime = Date.now()
      touchStartX = e.touches[0].clientX
      touchStartY = e.touches[0].clientY

      // Long press detection
      longPressTimer = setTimeout(() => {
        callbacks.onLongPress?.()
      }, 500)
    }

    const handleTouchMove = () => {
      clearTimeout(longPressTimer)
    }

    const handleTouchEnd = (e: TouchEvent) => {
      clearTimeout(longPressTimer)
      
      const touchEndTime = Date.now()
      const touchEndX = e.changedTouches[0].clientX
      const touchEndY = e.changedTouches[0].clientY
      
      const deltaX = touchEndX - touchStartX
      const deltaY = touchEndY - touchStartY
      const deltaTime = touchEndTime - touchStartTime
      
      // Tap detection
      if (Math.abs(deltaX) < 10 && Math.abs(deltaY) < 10 && deltaTime < 300) {
        const timeSinceLastTap = touchEndTime - lastTapTime
        
        if (timeSinceLastTap < 300) {
          callbacks.onDoubleTap?.()
        } else {
          callbacks.onTap?.()
        }
        
        lastTapTime = touchEndTime
        return
      }
      
      // Swipe detection
      if (deltaTime < 300 && (Math.abs(deltaX) > 50 || Math.abs(deltaY) > 50)) {
        if (Math.abs(deltaX) > Math.abs(deltaY)) {
          if (deltaX > 0) {
            callbacks.onSwipeRight?.()
          } else {
            callbacks.onSwipeLeft?.()
          }
        } else {
          if (deltaY > 0) {
            callbacks.onSwipeDown?.()
          } else {
            callbacks.onSwipeUp?.()
          }
        }
      }
    }

    element.addEventListener('touchstart', handleTouchStart, { passive: false })
    element.addEventListener('touchmove', handleTouchMove, { passive: false })
    element.addEventListener('touchend', handleTouchEnd, { passive: false })

    return () => {
      element.removeEventListener('touchstart', handleTouchStart)
      element.removeEventListener('touchmove', handleTouchMove)
      element.removeEventListener('touchend', handleTouchEnd)
      clearTimeout(longPressTimer)
    }
  }, [callbacks])
}

// Mobile navigation with gestures
export function MobileNavigation({
  isOpen,
  onClose,
  children
}: {
  isOpen: boolean
  onClose: () => void
  children: React.ReactNode
}) {
  const x = useMotionValue(0)

  const handleDragEnd = (event: any, info: PanInfo) => {
    if (info.offset.x < -100 || info.velocity.x < -500) {
      onClose()
    }
  }

  return (
    <>
      {/* Backdrop */}
      <motion.div
        className="fixed inset-0 bg-black z-40"
        initial={{ opacity: 0 }}
        animate={{ opacity: isOpen ? 0.5 : 0 }}
        exit={{ opacity: 0 }}
        onClick={onClose}
        style={{ pointerEvents: isOpen ? 'auto' : 'none' }}
      />
      
      {/* Navigation */}
      <motion.div
        className="fixed top-0 left-0 bottom-0 w-80 bg-white shadow-2xl z-50"
        style={{ x }}
        initial={{ x: '-100%' }}
        animate={{ x: isOpen ? 0 : '-100%' }}
        exit={{ x: '-100%' }}
        drag="x"
        dragConstraints={{ left: -300, right: 0 }}
        dragElastic={0.1}
        onDragEnd={handleDragEnd}
        transition={{ type: 'spring', damping: 30, stiffness: 300 }}
      >
        {children}
      </motion.div>
    </>
  )
}
